import { 
  ref, 
  uploadBytes, 
  uploadBytesResumable, 
  getDownloadURL, 
  deleteObject,
  listAll,
  UploadTaskSnapshot
} from 'firebase/storage';
import { storage } from '@/lib/firebase';

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  progress: number;
}

export interface UploadResult {
  url: string;
  path: string;
  name: string;
}

export class StorageService {
  // Upload a single image file
  static async uploadImage(
    file: File,
    path: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        throw new Error('File must be an image');
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error('Image size must be less than 5MB');
      }

      // Create unique filename
      const timestamp = Date.now();
      const fileName = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
      const fullPath = `${path}/${fileName}`;
      
      const storageRef = ref(storage, fullPath);

      // Upload with progress tracking if callback provided
      if (onProgress) {
        const uploadTask = uploadBytesResumable(storageRef, file);
        
        return new Promise((resolve, reject) => {
          uploadTask.on(
            'state_changed',
            (snapshot: UploadTaskSnapshot) => {
              const progress = {
                bytesTransferred: snapshot.bytesTransferred,
                totalBytes: snapshot.totalBytes,
                progress: (snapshot.bytesTransferred / snapshot.totalBytes) * 100
              };
              onProgress(progress);
            },
            (error) => {
              console.error('Upload error:', error);
              reject(new Error(`Upload failed: ${error.message}`));
            },
            async () => {
              try {
                const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
                resolve({
                  url: downloadURL,
                  path: fullPath,
                  name: fileName
                });
              } catch (error) {
                reject(new Error('Failed to get download URL'));
              }
            }
          );
        });
      } else {
        // Simple upload without progress tracking
        const snapshot = await uploadBytes(storageRef, file);
        const downloadURL = await getDownloadURL(snapshot.ref);
        
        return {
          url: downloadURL,
          path: fullPath,
          name: fileName
        };
      }
    } catch (error: any) {
      console.error('Error uploading image:', error);
      throw new Error(error.message || 'Failed to upload image');
    }
  }

  // Upload multiple images
  static async uploadMultipleImages(
    files: File[],
    path: string,
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): Promise<UploadResult[]> {
    try {
      const uploadPromises = files.map((file, index) => 
        this.uploadImage(
          file, 
          path, 
          onProgress ? (progress) => onProgress(index, progress) : undefined
        )
      );

      return await Promise.all(uploadPromises);
    } catch (error: any) {
      console.error('Error uploading multiple images:', error);
      throw new Error(error.message || 'Failed to upload images');
    }
  }

  // Delete an image by its storage path
  static async deleteImage(path: string): Promise<void> {
    try {
      const storageRef = ref(storage, path);
      await deleteObject(storageRef);
    } catch (error: any) {
      console.error('Error deleting image:', error);
      throw new Error(error.message || 'Failed to delete image');
    }
  }

  // Delete multiple images
  static async deleteMultipleImages(paths: string[]): Promise<void> {
    try {
      const deletePromises = paths.map(path => this.deleteImage(path));
      await Promise.all(deletePromises);
    } catch (error: any) {
      console.error('Error deleting multiple images:', error);
      throw new Error(error.message || 'Failed to delete images');
    }
  }

  // Get all images in a folder
  static async getImagesInFolder(folderPath: string): Promise<string[]> {
    try {
      const folderRef = ref(storage, folderPath);
      const result = await listAll(folderRef);
      
      const urlPromises = result.items.map(item => getDownloadURL(item));
      return await Promise.all(urlPromises);
    } catch (error: any) {
      console.error('Error getting images from folder:', error);
      throw new Error(error.message || 'Failed to get images');
    }
  }

  // Generate storage path for salon images
  static getSalonImagePath(salonId: string): string {
    return `salons/${salonId}/gallery`;
  }

  // Generate storage path for staff images
  static getStaffImagePath(salonId: string): string {
    return `salons/${salonId}/staff`;
  }

  // Generate storage path for service images
  static getServiceImagePath(salonId: string): string {
    return `salons/${salonId}/services`;
  }

  // Validate image file
  static validateImageFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { isValid: false, error: 'File must be an image' };
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return { isValid: false, error: 'Image size must be less than 5MB' };
    }

    // Check supported formats
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!supportedFormats.includes(file.type)) {
      return { isValid: false, error: 'Supported formats: JPEG, PNG, WebP' };
    }

    return { isValid: true };
  }

  // Extract storage path from download URL
  static extractPathFromUrl(url: string): string | null {
    try {
      // Firebase Storage URLs have a specific format
      const match = url.match(/\/o\/(.+?)\?/);
      if (match && match[1]) {
        return decodeURIComponent(match[1]);
      }
      return null;
    } catch (error) {
      console.error('Error extracting path from URL:', error);
      return null;
    }
  }
}
