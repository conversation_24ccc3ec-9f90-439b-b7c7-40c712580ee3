// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'salon_owner';
  createdAt: string;
  updatedAt: string;
}

export interface Admin extends User {
  role: 'admin';
  permissions: string[];
}

export interface SalonOwner extends User {
  role: 'salon_owner';
  salonId?: string;
  isApproved: boolean;
  requestedAt: string;
  approvedAt?: string;
  approvedBy?: string;
  registrationRequestId?: string;
  tempPassword?: string;
  mustChangePassword?: boolean;
  lastPasswordChange?: string;
}

// Salon and Business Types
export interface Salon {
  id: string;
  name: string;
  description: string;
  location: string;
  address: string;
  distance: string;
  rating: number;
  reviews: number;
  totalReviews: number;
  images: string[];
  ownerId: string;
  isActive: boolean;
  isApproved: boolean;
  approvalStatus: 'pending' | 'approved' | 'rejected' | 'content_review';
  registrationRequestId?: string;
  approvedAt?: string;
  approvedBy?: string;
  rejectedAt?: string;
  rejectedBy?: string;
  rejectionReason?: string;
  lastContentUpdate?: string;
  contentReviewStatus?: 'pending' | 'approved' | 'rejected';
  contentReviewedAt?: string;
  contentReviewedBy?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  phone?: string;
  email?: string;
  website?: string;
  instagram?: string;
  facebook?: string;
  openingHours?: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  amenities?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Service {
  id: string;
  salonId: string;
  name: string;
  description?: string;
  price: number;
  duration: number; // in minutes
  category: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Staff {
  id: string;
  salonId: string;
  name: string;
  email?: string;
  phone?: string;
  specialty: string;
  bio?: string;
  avatar?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StaffSchedule {
  id: string;
  staffId: string;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  isAvailable: boolean;
}

// Notification Types
export interface Notification {
  id: string;
  salonId: string;
  type: 'booking' | 'review' | 'staff' | 'payment' | 'system';
  title: string;
  message: string;
  isRead: boolean;
  isNew: boolean;
  priority: 'low' | 'medium' | 'high';
  relatedId?: string; // ID of related booking, review, etc.
  createdAt: string;
  readAt?: string;
}

// Notification Types
export interface Notification {
  id: string;
  salonId: string;
  type: 'booking' | 'review' | 'staff' | 'payment' | 'system';
  title: string;
  message: string;
  isRead: boolean;
  isNew: boolean;
  priority: 'low' | 'medium' | 'high';
  relatedId?: string; // ID of related booking, review, etc.
  createdAt: string;
  readAt?: string;
}

// Booking Types
export interface Booking {
  id: string;
  salonId: string;
  customerId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  serviceId: string;
  staffId: string;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  notes?: string;
  totalAmount: number;
  createdAt: string;
  updatedAt: string;
}

// Review Types
export interface Review {
  id: string;
  salonId: string;
  customerName: string;
  rating: number; // 1-5 stars
  comment: string;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
}

// Analytics Types
export interface AnalyticsData {
  totalRevenue: number;
  totalBookings: number;
  totalSalons: number;
  totalCustomers: number;
  revenueByMonth: { month: string; revenue: number }[];
  popularServices: { serviceName: string; bookings: number }[];
  peakHours: { hour: string; bookings: number }[];
  salonPerformance: { salonName: string; revenue: number; bookings: number }[];
}

// Request Types
export interface SalonRegistrationRequest {
  id: string;
  ownerName: string;
  ownerEmail: string;
  ownerPhone: string;
  salonName: string;
  salonDescription: string;
  salonAddress: string;
  businessLicense?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface SalonForm {
  name: string;
  description: string;
  location: string;
  address: string;
  images: string[];
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface ServiceForm {
  name: string;
  description?: string;
  price: number;
  duration: number;
  category: string;
}

// Pricing Types
export interface PricingRule {
  id: string;
  salonId: string;
  name: string;
  type: 'discount' | 'surcharge';
  value: number;
  valueType: 'percentage' | 'fixed';
  condition: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ServicePricing {
  id: string;
  salonId: string;
  serviceId: string;
  serviceName: string;
  basePrice: number;
  currentPrice: number;
  category: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PricingRuleForm {
  name: string;
  type: 'discount' | 'surcharge';
  value: number;
  valueType: 'percentage' | 'fixed';
  condition: string;
}

export interface StaffForm {
  name: string;
  email?: string;
  phone?: string;
  specialty: string;
  bio?: string;
  avatar?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
