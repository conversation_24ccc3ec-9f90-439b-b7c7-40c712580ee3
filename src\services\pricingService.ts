import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { PricingRule, ServicePricing, PricingRuleForm } from '@/types';

export class PricingService {
  private static readonly PRICING_RULES_COLLECTION = 'pricing_rules';
  private static readonly SERVICE_PRICING_COLLECTION = 'service_pricing';

  // ===== PRICING RULES =====

  // Create a new pricing rule
  static async createPricingRule(ruleData: PricingRuleForm & { salonId: string }): Promise<string> {
    try {
      const rule: Omit<PricingRule, 'id'> = {
        ...ruleData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return await FirestoreService.create<PricingRule>(this.PRICING_RULES_COLLECTION, rule);
    } catch (error) {
      console.error('Error creating pricing rule:', error);
      throw error;
    }
  }

  // Get pricing rules by salon
  static async getPricingRulesBySalon(salonId: string): Promise<PricingRule[]> {
    try {
      return await FirestoreService.getWithQuery<PricingRule>(this.PRICING_RULES_COLLECTION, [
        where('salonId', '==', salonId),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting pricing rules by salon:', error);
      throw error;
    }
  }

  // Update pricing rule
  static async updatePricingRule(id: string, updates: Partial<PricingRule>): Promise<void> {
    try {
      await FirestoreService.update<PricingRule>(this.PRICING_RULES_COLLECTION, id, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating pricing rule:', error);
      throw error;
    }
  }

  // Delete pricing rule (soft delete)
  static async deletePricingRule(id: string): Promise<void> {
    try {
      await FirestoreService.update<PricingRule>(this.PRICING_RULES_COLLECTION, id, { 
        isActive: false,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error deleting pricing rule:', error);
      throw error;
    }
  }

  // Toggle pricing rule active status
  static async togglePricingRule(id: string, isActive: boolean): Promise<void> {
    try {
      await FirestoreService.update<PricingRule>(this.PRICING_RULES_COLLECTION, id, { 
        isActive,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error toggling pricing rule:', error);
      throw error;
    }
  }

  // Listen to pricing rules changes for a salon
  static onSalonPricingRulesChange(salonId: string, callback: (rules: PricingRule[]) => void): () => void {
    return FirestoreService.onCollectionChange<PricingRule>(this.PRICING_RULES_COLLECTION, callback, [
      where('salonId', '==', salonId),
      orderBy('createdAt', 'desc')
    ]);
  }

  // ===== SERVICE PRICING =====

  // Create service pricing
  static async createServicePricing(pricingData: Omit<ServicePricing, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const pricing: Omit<ServicePricing, 'id'> = {
        ...pricingData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return await FirestoreService.create<ServicePricing>(this.SERVICE_PRICING_COLLECTION, pricing);
    } catch (error) {
      console.error('Error creating service pricing:', error);
      throw error;
    }
  }

  // Get service pricing by salon
  static async getServicePricingBySalon(salonId: string): Promise<ServicePricing[]> {
    try {
      return await FirestoreService.getWithQuery<ServicePricing>(this.SERVICE_PRICING_COLLECTION, [
        where('salonId', '==', salonId),
        where('isActive', '==', true),
        orderBy('serviceName', 'asc')
      ]);
    } catch (error) {
      console.error('Error getting service pricing by salon:', error);
      throw error;
    }
  }

  // Update service pricing
  static async updateServicePricing(id: string, updates: Partial<ServicePricing>): Promise<void> {
    try {
      await FirestoreService.update<ServicePricing>(this.SERVICE_PRICING_COLLECTION, id, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating service pricing:', error);
      throw error;
    }
  }

  // Update service price
  static async updateServicePrice(id: string, newPrice: number): Promise<void> {
    try {
      await FirestoreService.update<ServicePricing>(this.SERVICE_PRICING_COLLECTION, id, {
        currentPrice: newPrice,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating service price:', error);
      throw error;
    }
  }

  // Delete service pricing (soft delete)
  static async deleteServicePricing(id: string): Promise<void> {
    try {
      await FirestoreService.update<ServicePricing>(this.SERVICE_PRICING_COLLECTION, id, { 
        isActive: false,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error deleting service pricing:', error);
      throw error;
    }
  }

  // Listen to service pricing changes for a salon
  static onSalonServicePricingChange(salonId: string, callback: (pricing: ServicePricing[]) => void): () => void {
    return FirestoreService.onCollectionChange<ServicePricing>(this.SERVICE_PRICING_COLLECTION, callback, [
      where('salonId', '==', salonId),
      where('isActive', '==', true),
      orderBy('serviceName', 'asc')
    ]);
  }

  // Sync service pricing with services (create pricing entries for new services)
  static async syncServicePricing(salonId: string, services: Array<{ id: string; name: string; price: number; category: string }>): Promise<void> {
    try {
      const existingPricing = await this.getServicePricingBySalon(salonId);
      const existingServiceIds = existingPricing.map(p => p.serviceId);

      // Create pricing entries for new services
      const newServices = services.filter(service => !existingServiceIds.includes(service.id));
      
      for (const service of newServices) {
        await this.createServicePricing({
          salonId,
          serviceId: service.id,
          serviceName: service.name,
          basePrice: service.price,
          currentPrice: service.price,
          category: service.category
        });
      }
    } catch (error) {
      console.error('Error syncing service pricing:', error);
      throw error;
    }
  }

  // Calculate price with rules applied
  static calculatePriceWithRules(basePrice: number, rules: PricingRule[]): number {
    let finalPrice = basePrice;
    
    const activeRules = rules.filter(rule => rule.isActive);
    
    for (const rule of activeRules) {
      if (rule.type === 'discount') {
        if (rule.valueType === 'percentage') {
          finalPrice = finalPrice * (1 - rule.value / 100);
        } else {
          finalPrice = Math.max(0, finalPrice - rule.value);
        }
      } else if (rule.type === 'surcharge') {
        if (rule.valueType === 'percentage') {
          finalPrice = finalPrice * (1 + rule.value / 100);
        } else {
          finalPrice = finalPrice + rule.value;
        }
      }
    }
    
    return Math.round(finalPrice * 100) / 100; // Round to 2 decimal places
  }
}
