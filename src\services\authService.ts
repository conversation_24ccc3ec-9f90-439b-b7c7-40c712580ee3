import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  updateProfile,
  sendPasswordResetEmail,
  sendEmailVerification
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { User, Admin, SalonOwner } from '@/types';
import { SecurityLogService } from './securityLogService';

export class AuthService {
  // Generate temporary password
  static generateTempPassword(): string {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }
  // Sign in with email and password (admin and salon owners only)
  static async signIn(email: string, password: string): Promise<User | null> {
    try {
      // Validate input
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Get user data from Firestore
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (!userDoc.exists()) {
        // Sign out the user and throw error if no user document exists
        await this.signOut();
        throw new Error('Account not found. Only salon owners and administrators can sign in.');
      }

      const userData = { id: firebaseUser.uid, ...userDoc.data() } as User;

      // Only allow admin and salon_owner roles to sign in
      if (userData.role !== 'admin' && userData.role !== 'salon_owner') {
        await this.signOut();
        await SecurityLogService.logUnauthorizedAccess(
          'sign_in',
          'Attempted sign in with invalid role',
          { userEmail: email }
        );
        throw new Error('Access denied. Only salon owners and administrators can sign in.');
      }

      // Check if salon owner is approved
      if (userData.role === 'salon_owner') {
        const salonOwner = userData as SalonOwner;
        if (!salonOwner.isApproved) {
          await this.signOut();
          await SecurityLogService.logFailedLogin(
            email,
            'Salon owner account not yet approved'
          );
          throw new Error('Your salon registration is still pending approval. Please wait for admin approval.');
        }
      }

      return userData;
    } catch (error: any) {
      console.error('Sign in error:', error);

      // If it's our custom error, re-throw it
      if (error.message.includes('Only salon owners') ||
          error.message.includes('Access denied') ||
          error.message.includes('pending approval') ||
          error.message.includes('Account not found')) {
        throw error;
      }

      // Log failed login attempts and provide user-friendly error messages
      if (error?.code === 'auth/invalid-credential') {
        await SecurityLogService.logFailedLogin(email, 'Invalid credentials');
        throw new Error('Invalid email or password. Please check your credentials and try again.');
      } else if (error?.code === 'auth/user-not-found') {
        await SecurityLogService.logFailedLogin(email, 'User not found');
        throw new Error('No account found with this email address.');
      } else if (error?.code === 'auth/wrong-password') {
        await SecurityLogService.logFailedLogin(email, 'Wrong password');
        throw new Error('Incorrect password. Please try again.');
      } else if (error?.code === 'auth/too-many-requests') {
        await SecurityLogService.logRateLimitExceeded(email, 'sign_in', { userEmail: email });
        throw new Error('Too many failed attempts. Please try again later.');
      } else if (error?.code === 'auth/network-request-failed') {
        throw new Error('Network error. Please check your internet connection.');
      } else {
        await SecurityLogService.logFailedLogin(email, `Unknown error: ${error?.code || 'unknown'}`);
      }

      throw error;
    }
  }

  // Create new user account (admin use only for salon owners)
  static async createSalonOwnerAccount(
    email: string,
    tempPassword: string,
    userData: Partial<SalonOwner>,
    adminId: string
  ): Promise<User | null> {
    try {
      // Input validation
      if (!email || !tempPassword || !userData.name || !adminId) {
        throw new Error('Missing required parameters for account creation');
      }

      // Email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Invalid email format');
      }

      // Password strength validation
      if (tempPassword.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      const userCredential = await createUserWithEmailAndPassword(auth, email, tempPassword);
      const firebaseUser = userCredential.user;

      // Update Firebase Auth profile
      await updateProfile(firebaseUser, {
        displayName: userData.name
      });

      // Create salon owner document in Firestore
      const newSalonOwner: SalonOwner = {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        name: userData.name!,
        role: 'salon_owner',
        isApproved: true,
        requestedAt: new Date().toISOString(),
        approvedAt: new Date().toISOString(),
        approvedBy: adminId,
        tempPassword: tempPassword,
        mustChangePassword: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...userData
      };

      await setDoc(doc(db, 'users', firebaseUser.uid), newSalonOwner);

      // Send email verification
      await sendEmailVerification(firebaseUser);

      return newSalonOwner;
    } catch (error) {
      console.error('Create salon owner account error:', error);
      throw error;
    }
  }

  // Create admin account (restricted method)
  static async createAdminAccount(
    email: string,
    password: string,
    userData: Partial<Admin>
  ): Promise<User | null> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Update Firebase Auth profile
      await updateProfile(firebaseUser, {
        displayName: userData.name
      });

      // Create admin document in Firestore
      const newAdmin: Admin = {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        name: userData.name!,
        role: 'admin',
        permissions: userData.permissions || ['all'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...userData
      };

      await setDoc(doc(db, 'users', firebaseUser.uid), newAdmin);

      // Send email verification
      await sendEmailVerification(firebaseUser);

      return newAdmin;
    } catch (error) {
      console.error('Create admin account error:', error);
      throw error;
    }
  }

  // Sign out
  static async signOut(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  // Get current user data
  static async getCurrentUser(): Promise<User | null> {
    const firebaseUser = auth.currentUser;
    if (!firebaseUser) return null;

    try {
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (userDoc.exists()) {
        return { id: firebaseUser.uid, ...userDoc.data() } as User;
      }
      return null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Update user profile
  static async updateUserProfile(userId: string, updates: Partial<User>): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', userId), {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  // Send password reset email
  static async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  }

  // Listen to auth state changes
  static onAuthStateChanged(callback: (user: User | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        const user = await this.getCurrentUser();
        callback(user);
      } else {
        callback(null);
      }
    });
  }

  // Create salon owner account
  static async createSalonOwner(
    email: string,
    password: string,
    ownerData: Partial<SalonOwner>
  ): Promise<SalonOwner | null> {
    try {
      const salonOwner: Partial<SalonOwner> = {
        ...ownerData,
        role: 'salon_owner',
        isApproved: false,
        requestedAt: new Date().toISOString()
      };

      const user = await this.signUp(email, password, salonOwner);
      return user as SalonOwner;
    } catch (error) {
      console.error('Create salon owner error:', error);
      throw error;
    }
  }

  // Approve salon owner
  static async approveSalonOwner(userId: string, approvedBy: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', userId), {
        isApproved: true,
        approvedAt: new Date().toISOString(),
        approvedBy,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Approve salon owner error:', error);
      throw error;
    }
  }
}
