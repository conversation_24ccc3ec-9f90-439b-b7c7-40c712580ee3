import React from 'react';
import { Heart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  text?: string;
  className?: string;
  showText?: boolean;
}

const sizeClasses = {
  sm: {
    container: 'w-6 h-6',
    icon: 'w-4 h-4',
    text: 'text-sm'
  },
  md: {
    container: 'w-8 h-8',
    icon: 'w-5 h-5',
    text: 'text-base'
  },
  lg: {
    container: 'w-12 h-12',
    icon: 'w-8 h-8',
    text: 'text-lg'
  },
  xl: {
    container: 'w-16 h-16',
    icon: 'w-12 h-12',
    text: 'text-xl'
  }
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  text = 'Loading...',
  className,
  showText = true
}) => {
  const classes = sizeClasses[size];

  return (
    <div className={cn('flex flex-col items-center justify-center', className)}>
      <div className={cn(
        'bg-glamspot-primary rounded-lg flex items-center justify-center animate-pulse',
        classes.container
      )}>
        <Heart className={cn('text-white fill-current animate-bounce', classes.icon)} />
      </div>
      {showText && (
        <p className={cn('text-glamspot-neutral-600 mt-2', classes.text)}>
          {text}
        </p>
      )}
    </div>
  );
};

// Full page loading component
export const FullPageLoader: React.FC<{
  text?: string;
  className?: string;
}> = ({ text = 'Loading...', className }) => {
  return (
    <div className={cn(
      'min-h-screen bg-glamspot-neutral-50 flex items-center justify-center',
      className
    )}>
      <LoadingSpinner size="lg" text={text} />
    </div>
  );
};

// Inline loading component for smaller areas
export const InlineLoader: React.FC<{
  text?: string;
  className?: string;
  height?: string;
}> = ({ text = 'Loading...', className, height = 'h-64' }) => {
  return (
    <div className={cn(
      'flex items-center justify-center',
      height,
      className
    )}>
      <LoadingSpinner size="md" text={text} />
    </div>
  );
};
