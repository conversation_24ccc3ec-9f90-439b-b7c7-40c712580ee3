import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Notification } from '@/types';
import { useAuth } from './AuthContext';
import { toast } from 'sonner';
import { NotificationService } from '@/services/notificationService';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  clearAll: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}



export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user, isSalonOwner } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  // Load notifications from Firebase for salon owners
  useEffect(() => {
    if (!isSalonOwner || !user?.salonId) {
      setLoading(false);
      return;
    }

    const loadNotifications = async () => {
      try {
        setLoading(true);
        const notificationsData = await NotificationService.getNotificationsBySalon(user.salonId);
        setNotifications(notificationsData);
      } catch (error) {
        console.error('Error loading notifications:', error);
        toast.error('Failed to load notifications');
      } finally {
        setLoading(false);
      }
    };

    loadNotifications();

    // Set up real-time listener for notifications
    const unsubscribe = NotificationService.onSalonNotificationsChange(user.salonId, (notificationsData) => {
      setNotifications(notificationsData);
    });

    return () => unsubscribe();
  }, [user, isSalonOwner]);

  const unreadCount = notifications.filter(notif => !notif.isRead).length;

  const addNotification = async (notificationData: Omit<Notification, 'id' | 'createdAt'>) => {
    try {
      await NotificationService.createNotification(notificationData);

      // Show toast for high priority notifications
      if (notificationData.priority === 'high') {
        toast.success(notificationData.title, {
          description: notificationData.message,
        });
      }
    } catch (error) {
      console.error('Error creating notification:', error);
      toast.error('Failed to create notification');
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await NotificationService.markAsRead(notificationId);
      // The real-time listener will update the state automatically
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
    }
  };

  const markAllAsRead = async () => {
    if (!user?.salonId) return;

    try {
      await NotificationService.markAllAsRead(user.salonId);
      // The real-time listener will update the state automatically
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      await NotificationService.deleteNotification(notificationId);
      // The real-time listener will update the state automatically
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast.error('Failed to delete notification');
    }
  };

  const clearAll = async () => {
    if (!user?.salonId) return;

    try {
      await NotificationService.clearAllNotifications(user.salonId);
      // The real-time listener will update the state automatically
    } catch (error) {
      console.error('Error clearing all notifications:', error);
      toast.error('Failed to clear all notifications');
    }
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
