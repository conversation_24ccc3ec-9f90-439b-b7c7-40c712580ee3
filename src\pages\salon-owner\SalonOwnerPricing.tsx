import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DollarSign,
  Plus,
  Edit,
  Trash2,
  Clock,
  TrendingUp,
  TrendingDown,
  Percent,
  Tag
} from 'lucide-react';
import { toast } from 'sonner';
import { PricingRule, ServicePricing, Service } from '@/types';
import { PricingService } from '@/services/pricingService';
import { ServiceService } from '@/services/serviceService';
import { useAuth } from '@/contexts/AuthContext';

const SalonOwnerPricing = () => {
  const { user } = useAuth();
  const [isAddRuleDialogOpen, setIsAddRuleDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<PricingRule | null>(null);
  const [loading, setLoading] = useState(true);

  // Firebase state
  const [pricingRules, setPricingRules] = useState<PricingRule[]>([]);
  const [servicePricing, setServicePricing] = useState<ServicePricing[]>([]);
  const [services, setServices] = useState<Service[]>([]);

  // Load data from Firebase
  useEffect(() => {
    const loadData = async () => {
      if (!user || user.role !== 'salon_owner' || !user.salonId) return;

      try {
        setLoading(true);

        // Load services, pricing rules, and service pricing
        const [servicesData, rulesData, pricingData] = await Promise.all([
          ServiceService.getServicesBySalon(user.salonId),
          PricingService.getPricingRulesBySalon(user.salonId),
          PricingService.getServicePricingBySalon(user.salonId)
        ]);

        setServices(servicesData);
        setPricingRules(rulesData);
        setServicePricing(pricingData);

        // Sync service pricing with services
        await PricingService.syncServicePricing(user.salonId, servicesData);

      } catch (error) {
        console.error('Error loading pricing data:', error);
        toast.error('Failed to load pricing data');
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Set up real-time listeners
    if (user?.salonId) {
      const unsubscribeRules = PricingService.onSalonPricingRulesChange(user.salonId, (rulesData) => {
        setPricingRules(rulesData);
      });

      const unsubscribePricing = PricingService.onSalonServicePricingChange(user.salonId, (pricingData) => {
        setServicePricing(pricingData);
      });

      const unsubscribeServices = ServiceService.onSalonServicesChange(user.salonId, (servicesData) => {
        setServices(servicesData);
      });

      return () => {
        unsubscribeRules();
        unsubscribePricing();
        unsubscribeServices();
      };
    }
  }, [user]);

  const [ruleFormData, setRuleFormData] = useState({
    name: '',
    type: 'discount' as 'discount' | 'surcharge',
    value: '',
    valueType: 'percentage' as 'percentage' | 'fixed',
    condition: '',
  });

  const handleAddRule = () => {
    setRuleFormData({
      name: '',
      type: 'discount',
      value: '',
      valueType: 'percentage',
      condition: '',
    });
    setEditingRule(null);
    setIsAddRuleDialogOpen(true);
  };

  const handleEditRule = (rule: PricingRule) => {
    setRuleFormData({
      name: rule.name,
      type: rule.type,
      value: rule.value.toString(),
      valueType: rule.valueType,
      condition: rule.condition,
    });
    setEditingRule(rule);
    setIsAddRuleDialogOpen(true);
  };

  const handleSubmitRule = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!ruleFormData.name || !ruleFormData.value || !ruleFormData.condition || !user?.salonId) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const ruleData = {
        salonId: user.salonId,
        name: ruleFormData.name,
        type: ruleFormData.type,
        value: parseFloat(ruleFormData.value),
        valueType: ruleFormData.valueType,
        condition: ruleFormData.condition,
      };

      if (editingRule) {
        await PricingService.updatePricingRule(editingRule.id, ruleData);
        toast.success('Pricing rule updated successfully!');
      } else {
        await PricingService.createPricingRule(ruleData);
        toast.success('Pricing rule added successfully!');
      }

      setIsAddRuleDialogOpen(false);
      setEditingRule(null);
      setRuleFormData({
        name: '',
        type: 'discount',
        value: '',
        valueType: 'percentage',
        condition: '',
      });
    } catch (error) {
      console.error('Error saving pricing rule:', error);
      toast.error('Failed to save pricing rule');
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    try {
      await PricingService.deletePricingRule(ruleId);
      toast.success('Pricing rule deleted successfully!');
    } catch (error) {
      console.error('Error deleting pricing rule:', error);
      toast.error('Failed to delete pricing rule');
    }
  };

  const handleToggleRule = async (ruleId: string) => {
    try {
      const rule = pricingRules.find(r => r.id === ruleId);
      if (rule) {
        await PricingService.togglePricingRule(ruleId, !rule.isActive);
      }
    } catch (error) {
      console.error('Error toggling pricing rule:', error);
      toast.error('Failed to toggle pricing rule');
    }
  };

  const handlePriceUpdate = async (pricingId: string, newPrice: number) => {
    try {
      await PricingService.updateServicePrice(pricingId, newPrice);
      toast.success('Price updated successfully!');
    } catch (error) {
      console.error('Error updating price:', error);
      toast.error('Failed to update price');
    }
  };

  const totalRevenue = servicePricing.reduce((sum, pricing) => sum + (pricing.currentPrice * 10), 0); // Mock calculation
  const averagePrice = servicePricing.reduce((sum, pricing) => sum + pricing.currentPrice, 0) / servicePricing.length;
  const activeRules = pricingRules.filter(rule => rule.isActive).length;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Pricing Management</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage your service pricing and special offers
          </p>
        </div>
        <Button onClick={handleAddRule} className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
          <Plus className="w-4 h-4 mr-2" />
          Add Pricing Rule
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Services
            </CardTitle>
            <DollarSign className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{servicePricing.length}</div>
            <p className="text-xs text-green-600 mt-1">
              {servicePricing.filter(s => s.isActive).length} active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Average Price
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">${averagePrice.toFixed(0)}</div>
            <p className="text-xs text-green-600 mt-1">
              +5% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Active Rules
            </CardTitle>
            <Tag className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{activeRules}</div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              Pricing rules active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Monthly Revenue
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">${totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-green-600 mt-1">
              +12% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Service Pricing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-glamspot-primary" />
              Service Pricing
            </CardTitle>
            <CardDescription>
              Manage individual service prices
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Service</TableHead>
                  <TableHead>Base Price</TableHead>
                  <TableHead>Current Price</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {servicePricing.map((pricing) => (
                  <TableRow key={pricing.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{pricing.serviceName}</p>
                        <p className="text-sm text-glamspot-neutral-500">{pricing.category}</p>
                      </div>
                    </TableCell>
                    <TableCell>${pricing.basePrice}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">${pricing.currentPrice}</span>
                        {pricing.currentPrice !== pricing.basePrice && (
                          <Badge variant="outline" className="text-xs">
                            {pricing.currentPrice > pricing.basePrice ? '+' : ''}
                            {((pricing.currentPrice - pricing.basePrice) / pricing.basePrice * 100).toFixed(0)}%
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Pricing Rules */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Percent className="w-5 h-5 text-glamspot-primary" />
              Pricing Rules
            </CardTitle>
            <CardDescription>
              Special discounts and surcharges
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pricingRules.map((rule) => (
                <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Switch
                      checked={rule.isActive}
                      onCheckedChange={() => handleToggleRule(rule.id)}
                    />
                    <div>
                      <p className="font-medium">{rule.name}</p>
                      <p className="text-sm text-glamspot-neutral-500">{rule.condition}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={rule.type === 'discount' ? 'default' : 'destructive'}>
                      {rule.type === 'discount' ? (
                        <TrendingDown className="w-3 h-3 mr-1" />
                      ) : (
                        <TrendingUp className="w-3 h-3 mr-1" />
                      )}
                      {rule.valueType === 'percentage' ? `${rule.value}%` : `$${rule.value}`}
                    </Badge>
                    <Button variant="ghost" size="sm" onClick={() => handleEditRule(rule)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteRule(rule.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add/Edit Pricing Rule Dialog */}
      <Dialog open={isAddRuleDialogOpen} onOpenChange={setIsAddRuleDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{editingRule ? 'Edit' : 'Add'} Pricing Rule</DialogTitle>
            <DialogDescription>
              {editingRule ? 'Update the pricing rule details' : 'Create a new pricing rule for your salon'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitRule} className="space-y-4">
            <div>
              <Label htmlFor="ruleName">Rule Name</Label>
              <Input
                id="ruleName"
                value={ruleFormData.name}
                onChange={(e) => setRuleFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Senior Discount"
                required
              />
            </div>
            <div>
              <Label htmlFor="ruleType">Type</Label>
              <Select value={ruleFormData.type} onValueChange={(value: 'discount' | 'surcharge') => 
                setRuleFormData(prev => ({ ...prev, type: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="discount">Discount</SelectItem>
                  <SelectItem value="surcharge">Surcharge</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="ruleValue">Value</Label>
                <Input
                  id="ruleValue"
                  type="number"
                  value={ruleFormData.value}
                  onChange={(e) => setRuleFormData(prev => ({ ...prev, value: e.target.value }))}
                  placeholder="10"
                  required
                />
              </div>
              <div>
                <Label htmlFor="valueType">Type</Label>
                <Select value={ruleFormData.valueType} onValueChange={(value: 'percentage' | 'fixed') => 
                  setRuleFormData(prev => ({ ...prev, valueType: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">%</SelectItem>
                    <SelectItem value="fixed">$</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="condition">Condition</Label>
              <Input
                id="condition"
                value={ruleFormData.condition}
                onChange={(e) => setRuleFormData(prev => ({ ...prev, condition: e.target.value }))}
                placeholder="e.g., Age 65+"
                required
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setIsAddRuleDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
                {editingRule ? 'Update' : 'Add'} Rule
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SalonOwnerPricing;
