import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Sciss<PERSON>,
  Clock,
  DollarSign,
  Building2
} from 'lucide-react';
import { Service, Salon } from '@/types';
import { ServiceService } from '@/services/serviceService';
import { SalonService } from '@/services/salonService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { useEffect } from 'react';

const ServiceManagement = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [salonFilter, setSalonFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [salons, setSalons] = useState<Salon[]>([]);
  const [loading, setLoading] = useState(true);

  // Form state for add/edit
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    duration: '',
    category: '',
    salonId: '',
  });

  // Load services and salons from Firebase
  useEffect(() => {
    const loadData = async () => {
      if (!user || user.role !== 'admin') return;

      try {
        setLoading(true);
        const [servicesData, salonsData] = await Promise.all([
          ServiceService.getAllServices(),
          SalonService.getAllSalons()
        ]);

        setServices(servicesData);
        setSalons(salonsData);
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load services and salons');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user]);

  // Create salon lookup
  const salonLookup = salons.reduce((acc, salon) => {
    acc[salon.id] = salon.name;
    return acc;
  }, {} as Record<string, string>);

  const categories = ['Hair', 'Skincare', 'Nails', 'Massage', 'Waxing', 'Makeup'];

  const filteredServices = services.filter(service => {
    const matchesSearch = 
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      salonLookup[service.salonId]?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSalon = salonFilter === 'all' || service.salonId === salonFilter;
    const matchesCategory = categoryFilter === 'all' || service.category === categoryFilter;
    
    return matchesSearch && matchesSalon && matchesCategory;
  });

  const handleViewService = (service: Service) => {
    setSelectedService(service);
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setFormData({
      name: service.name,
      description: service.description || '',
      price: service.price.toString(),
      duration: service.duration.toString(),
      category: service.category,
      salonId: service.salonId,
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteService = async (service: Service) => {
    try {
      await ServiceService.deleteService(service.id);
      toast.success(`Service "${service.name}" has been deleted`);

      // Reload services data
      const servicesData = await ServiceService.getAllServices();
      setServices(servicesData);
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service. Please try again.');
    }
  };

  const handleAddService = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      duration: '',
      category: '',
      salonId: '',
    });
    setIsAddDialogOpen(true);
  };

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.price || !formData.duration || !formData.category || !formData.salonId) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      if (editingService) {
        // Update existing service
        await ServiceService.updateService(editingService.id, {
          name: formData.name,
          description: formData.description,
          price: parseFloat(formData.price),
          duration: parseInt(formData.duration),
          category: formData.category,
          salonId: formData.salonId
        });
        toast.success(`Service "${formData.name}" has been updated`);
        setIsEditDialogOpen(false);
        setEditingService(null);
      } else {
        // Create new service
        await ServiceService.createService({
          name: formData.name,
          description: formData.description,
          price: parseFloat(formData.price),
          duration: parseInt(formData.duration),
          category: formData.category,
          salonId: formData.salonId
        });
        toast.success(`Service "${formData.name}" has been added`);
        setIsAddDialogOpen(false);
      }

      // Reload services data
      const servicesData = await ServiceService.getAllServices();
      setServices(servicesData);

    } catch (error) {
      console.error('Error saving service:', error);
      toast.error('Failed to save service. Please try again.');
    }

    setFormData({
      name: '',
      description: '',
      price: '',
      duration: '',
      category: '',
      salonId: '',
    });
  };

  const ServiceForm = () => (
    <form onSubmit={handleSubmitForm} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Service Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter service name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="category">Category *</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="salon">Salon *</Label>
        <Select value={formData.salonId} onValueChange={(value) => setFormData(prev => ({ ...prev, salonId: value }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select salon" />
          </SelectTrigger>
          <SelectContent>
            {salons.map(salon => (
              <SelectItem key={salon.id} value={salon.id}>{salon.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="price">Price ($) *</Label>
          <Input
            id="price"
            type="number"
            min="0"
            step="0.01"
            value={formData.price}
            onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
            placeholder="0.00"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="duration">Duration (minutes) *</Label>
          <Input
            id="duration"
            type="number"
            min="15"
            step="15"
            value={formData.duration}
            onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
            placeholder="60"
            required
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Enter service description"
          className="min-h-[80px]"
        />
      </div>
      
      <div className="flex justify-end gap-3">
        <Button 
          type="button" 
          variant="outline" 
          onClick={() => {
            setIsAddDialogOpen(false);
            setIsEditDialogOpen(false);
            setEditingService(null);
          }}
        >
          Cancel
        </Button>
        <Button type="submit" className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
          {editingService ? 'Update Service' : 'Add Service'}
        </Button>
      </div>
    </form>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-glamspot-primary mx-auto"></div>
          <p className="mt-2 text-glamspot-neutral-600">Loading services...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Service Management</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage services, pricing, and duration across all salons
          </p>
        </div>
        <Button onClick={handleAddService} className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
          <Plus className="w-4 h-4 mr-2" />
          Add Service
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-glamspot-neutral-400 w-4 h-4" />
              <Input
                placeholder="Search services by name, description, or salon..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={salonFilter} onValueChange={setSalonFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by salon" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Salons</SelectItem>
                {salons.map(salon => (
                  <SelectItem key={salon.id} value={salon.id}>{salon.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Badge variant="outline" className="text-glamspot-neutral-600">
              {filteredServices.length} service{filteredServices.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Services Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scissors className="w-5 h-5 text-glamspot-primary" />
            All Services
          </CardTitle>
          <CardDescription>
            Complete list of all services across all salons
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service</TableHead>
                <TableHead>Salon</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredServices.map((service) => (
                <TableRow key={service.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-glamspot-neutral-200 rounded-lg flex items-center justify-center">
                        <Scissors className="w-5 h-5 text-glamspot-neutral-600" />
                      </div>
                      <div>
                        <p className="font-medium text-glamspot-neutral-900">{service.name}</p>
                        <p className="text-sm text-glamspot-neutral-500 truncate max-w-xs">
                          {service.description}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Building2 className="w-4 h-4 text-glamspot-neutral-500" />
                      <span className="text-sm">{salonLookup[service.salonId]}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{service.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <DollarSign className="w-4 h-4 text-green-600" />
                      <span className="font-medium">${service.price}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4 text-glamspot-neutral-500" />
                      <span className="text-sm">{service.duration} min</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={service.isActive ? 'default' : 'secondary'}
                      className={service.isActive ? 'bg-green-100 text-green-800' : ''}
                    >
                      {service.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewService(service)}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditService(service)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Service
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteService(service)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete Service
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Service Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Service</DialogTitle>
            <DialogDescription>
              Create a new service for a salon
            </DialogDescription>
          </DialogHeader>
          <ServiceForm />
        </DialogContent>
      </Dialog>

      {/* Edit Service Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Service</DialogTitle>
            <DialogDescription>
              Update service information
            </DialogDescription>
          </DialogHeader>
          <ServiceForm />
        </DialogContent>
      </Dialog>

      {/* Service Details Dialog */}
      <Dialog open={!!selectedService} onOpenChange={() => setSelectedService(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedService?.name}</DialogTitle>
            <DialogDescription>Service details and information</DialogDescription>
          </DialogHeader>
          {selectedService && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Salon</label>
                  <p className="text-glamspot-neutral-900">{salonLookup[selectedService.salonId]}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Category</label>
                  <p className="text-glamspot-neutral-900">{selectedService.category}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Price</label>
                  <p className="text-glamspot-neutral-900 text-lg font-semibold">${selectedService.price}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Duration</label>
                  <p className="text-glamspot-neutral-900">{selectedService.duration} minutes</p>
                </div>
              </div>
              {selectedService.description && (
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Description</label>
                  <p className="text-glamspot-neutral-900">{selectedService.description}</p>
                </div>
              )}
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Status</label>
                <Badge
                  variant={selectedService.isActive ? 'default' : 'secondary'}
                  className={selectedService.isActive ? 'bg-green-100 text-green-800 ml-2' : 'ml-2'}
                >
                  {selectedService.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ServiceManagement;
