import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Camera,
  Plus,
  X,
  Eye,
  Save,
  Trash2,
  Link as LinkIcon,
  Upload,
  AlertCircle,
  FileImage,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { StorageService, UploadProgress } from '@/services/storageService';

interface GalleryManagerProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  isEditing: boolean;
  maxImages?: number;
  salonId?: string; // For Firebase Storage path
}

export const GalleryManager: React.FC<GalleryManagerProps> = ({
  images,
  onImagesChange,
  isEditing,
  maxImages = 10,
  salonId
}) => {
  const [newImageUrl, setNewImageUrl] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: number]: number }>({});
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateImageUrl = async (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    });
  };

  const handlePreviewImage = async () => {
    if (!newImageUrl.trim()) {
      toast.error('Please enter an image URL');
      return;
    }

    setIsValidating(true);
    try {
      const isValid = await validateImageUrl(newImageUrl);
      if (isValid) {
        setPreviewUrl(newImageUrl);
        toast.success('Image loaded successfully!');
      } else {
        toast.error('Invalid image URL. Please check the URL and try again.');
        setPreviewUrl(null);
      }
    } catch (error) {
      toast.error('Error loading image. Please check the URL.');
      setPreviewUrl(null);
    } finally {
      setIsValidating(false);
    }
  };

  const handleAddImage = () => {
    if (!previewUrl) {
      toast.error('Please preview the image first');
      return;
    }

    if (images.length >= maxImages) {
      toast.error(`Maximum ${maxImages} images allowed`);
      return;
    }

    if (images.includes(previewUrl)) {
      toast.error('This image is already in your gallery');
      return;
    }

    const updatedImages = [...images, previewUrl];
    onImagesChange(updatedImages);
    setNewImageUrl('');
    setPreviewUrl(null);
    toast.success('Image added to gallery!');
  };

  const handleRemoveImage = (index: number) => {
    const updatedImages = images.filter((_, i) => i !== index);
    onImagesChange(updatedImages);
    toast.success('Image removed from gallery');
  };

  const handleMoveImage = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= images.length) return;

    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    onImagesChange(updatedImages);
  };

  // Handle file selection
  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    if (!salonId) {
      toast.error('Salon ID is required for file upload');
      return;
    }

    // Check if adding these files would exceed the limit
    if (images.length + files.length > maxImages) {
      toast.error(`Cannot upload ${files.length} files. Maximum ${maxImages} images allowed.`);
      return;
    }

    // Validate all files first
    const fileArray = Array.from(files);
    for (const file of fileArray) {
      const validation = StorageService.validateImageFile(file);
      if (!validation.isValid) {
        toast.error(`${file.name}: ${validation.error}`);
        return;
      }
    }

    setIsUploading(true);
    setUploadProgress({});

    try {
      const storagePath = StorageService.getSalonImagePath(salonId);

      // Upload files with progress tracking
      const uploadResults = await StorageService.uploadMultipleImages(
        fileArray,
        storagePath,
        (fileIndex: number, progress: UploadProgress) => {
          setUploadProgress(prev => ({
            ...prev,
            [fileIndex]: progress.progress
          }));
        }
      );

      // Add uploaded image URLs to the gallery
      const newImageUrls = uploadResults.map(result => result.url);
      const updatedImages = [...images, ...newImageUrls];
      onImagesChange(updatedImages);

      toast.success(`Successfully uploaded ${uploadResults.length} image(s)!`);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('Error uploading files:', error);
      toast.error(error.message || 'Failed to upload images');
    } finally {
      setIsUploading(false);
      setUploadProgress({});
    }
  };

  // Handle removing image (with Firebase Storage cleanup)
  const handleRemoveImageWithCleanup = async (index: number) => {
    const imageUrl = images[index];

    // Try to delete from Firebase Storage if it's a Firebase URL
    if (salonId && imageUrl.includes('firebase')) {
      try {
        const storagePath = StorageService.extractPathFromUrl(imageUrl);
        if (storagePath) {
          await StorageService.deleteImage(storagePath);
        }
      } catch (error) {
        console.warn('Could not delete image from storage:', error);
        // Continue with removal from array even if storage deletion fails
      }
    }

    // Remove from images array
    handleRemoveImage(index);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="w-5 h-5 text-glamspot-primary" />
          Gallery Management
        </CardTitle>
        <CardDescription>
          Add and manage your salon's photo gallery. Use high-quality images to showcase your salon and services.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add New Image Section */}
        {isEditing && (
          <div className="space-y-4 p-4 border border-glamspot-neutral-200 rounded-lg bg-glamspot-neutral-50">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Plus className="w-4 h-4 text-glamspot-primary" />
                <h3 className="font-medium text-glamspot-neutral-900">Add New Images</h3>
                <Badge variant="secondary" className="text-xs">
                  {images.length}/{maxImages}
                </Badge>
              </div>
            </div>

            {/* Upload Methods */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* File Upload */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-glamspot-neutral-700">Upload from Device</h4>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button
                  onClick={handleFileSelect}
                  variant="outline"
                  className="w-full h-20 border-dashed border-2 border-glamspot-neutral-300 hover:border-glamspot-primary"
                  disabled={isUploading || images.length >= maxImages}
                >
                  <div className="flex flex-col items-center gap-2">
                    {isUploading ? (
                      <Loader2 className="w-6 h-6 animate-spin text-glamspot-primary" />
                    ) : (
                      <Upload className="w-6 h-6 text-glamspot-primary" />
                    )}
                    <span className="text-sm">
                      {isUploading ? 'Uploading...' : 'Choose Files'}
                    </span>
                  </div>
                </Button>
                <p className="text-xs text-glamspot-neutral-500 text-center">
                  JPEG, PNG, WebP • Max 5MB each
                </p>
              </div>

              {/* URL Input */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-glamspot-neutral-700">Add from URL</h4>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="image-url">Image URL</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="image-url"
                        placeholder="https://example.com/image.jpg"
                        value={newImageUrl}
                        onChange={(e) => setNewImageUrl(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handlePreviewImage()}
                        disabled={isUploading}
                      />
                      <Button
                        onClick={handlePreviewImage}
                        disabled={!newImageUrl.trim() || isValidating || isUploading}
                        variant="outline"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        {isValidating ? 'Loading...' : 'Preview'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Upload Progress */}
            {isUploading && Object.keys(uploadProgress).length > 0 && (
              <div className="space-y-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900">Upload Progress</h4>
                {Object.entries(uploadProgress).map(([fileIndex, progress]) => (
                  <div key={fileIndex} className="space-y-1">
                    <div className="flex justify-between text-xs text-blue-700">
                      <span>File {parseInt(fileIndex) + 1}</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>
                ))}
              </div>
            )}

            {/* Preview Section */}
            {previewUrl && (
              <div className="space-y-3">
                <div className="aspect-video w-full max-w-md rounded-lg overflow-hidden bg-glamspot-neutral-100">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={handleAddImage}
                    className="bg-glamspot-primary hover:bg-glamspot-primary-dark"
                    disabled={images.length >= maxImages}
                  >
                    <Save className="w-4 h-4 mr-1" />
                    Add to Gallery
                  </Button>
                  <Button
                    onClick={() => {
                      setPreviewUrl(null);
                      setNewImageUrl('');
                    }}
                    variant="outline"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {images.length >= maxImages && (
              <div className="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <AlertCircle className="w-4 h-4 text-orange-600" />
                <span className="text-sm text-orange-700">
                  Maximum number of images reached. Remove an image to add a new one.
                </span>
              </div>
            )}
          </div>
        )}

        {/* Current Gallery */}
        <div>
          <h3 className="font-medium text-glamspot-neutral-900 mb-3">
            Current Gallery ({images.length} images)
          </h3>
          
          {images.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {images.map((image, index) => (
                <div key={index} className="group relative aspect-square rounded-lg overflow-hidden bg-glamspot-neutral-100">
                  <img
                    src={image}
                    alt={`Gallery image ${index + 1}`}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  
                  {/* Image Controls Overlay */}
                  {isEditing && (
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <div className="flex gap-2">
                        {index > 0 && (
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleMoveImage(index, index - 1)}
                            className="h-8 w-8 p-0"
                          >
                            ←
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleRemoveImageWithCleanup(index)}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                        {index < images.length - 1 && (
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleMoveImage(index, index + 1)}
                            className="h-8 w-8 p-0"
                          >
                            →
                          </Button>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Primary Image Badge */}
                  {index === 0 && (
                    <div className="absolute top-2 left-2">
                      <Badge className="bg-glamspot-primary text-white text-xs">
                        Primary
                      </Badge>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Camera className="h-12 w-12 text-glamspot-neutral-300 mx-auto mb-4" />
              <p className="text-glamspot-neutral-500">No photos added yet</p>
              <p className="text-sm text-glamspot-neutral-400">
                {isEditing ? 'Add photos to showcase your salon and services' : 'Photos will appear here once added'}
              </p>
            </div>
          )}
        </div>

        {/* Tips Section */}
        {isEditing && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Tips for great gallery photos:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Use high-resolution images (at least 1200x800 pixels)</li>
              <li>• Show different angles of your salon interior</li>
              <li>• Include photos of your services in action</li>
              <li>• Ensure good lighting and clear focus</li>
              <li>• The first image will be used as your primary salon photo</li>
              <li>• Upload directly from your device for best quality</li>
              <li>• Supported formats: JPEG, PNG, WebP (max 5MB each)</li>
              <li>• You can upload multiple images at once</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
