import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Heart, Building2, User, Mail, Phone, MapPin, FileText, CheckCircle } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { SalonRequestService } from '@/services/salonRequestService';
import { InputSanitizer, InputValidator, RateLimiter } from '@/utils/security';

const SalonOwnerRegistration = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitAttempts, setSubmitAttempts] = useState(0);
  const [lastSubmitTime, setLastSubmitTime] = useState<number>(0);
  const [formData, setFormData] = useState({
    ownerName: '',
    ownerEmail: '',
    ownerPhone: '',
    salonName: '',
    salonDescription: '',
    salonAddress: '',
    businessLicense: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    // Use security utilities for input sanitization
    let sanitizedValue = value;

    switch (field) {
      case 'ownerName':
      case 'salonName':
        sanitizedValue = InputSanitizer.sanitizeText(value, 100);
        break;
      case 'ownerEmail':
        sanitizedValue = InputSanitizer.sanitizeEmail(value);
        break;
      case 'ownerPhone':
        sanitizedValue = InputSanitizer.sanitizePhone(value);
        break;
      case 'salonDescription':
        sanitizedValue = InputSanitizer.sanitizeText(value, 1000);
        break;
      case 'salonAddress':
        sanitizedValue = InputSanitizer.sanitizeText(value, 200);
        break;
      case 'businessLicense':
        sanitizedValue = InputSanitizer.sanitizeText(value, 50);
        break;
      default:
        sanitizedValue = InputSanitizer.sanitizeText(value);
    }

    setFormData(prev => ({ ...prev, [field]: sanitizedValue }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = async () => {
    const newErrors: Record<string, string> = {};

    if (!formData.ownerName.trim()) {
      newErrors.ownerName = 'Owner name is required';
    } else if (formData.ownerName.trim().length < 2) {
      newErrors.ownerName = 'Owner name must be at least 2 characters long';
    }

    if (!formData.ownerEmail.trim()) {
      newErrors.ownerEmail = 'Email is required';
    } else if (!InputValidator.isValidEmail(formData.ownerEmail)) {
      newErrors.ownerEmail = 'Please enter a valid email address';
    } else {
      // Check for duplicate email in existing requests
      try {
        const existingRequests = await SalonRequestService.getRequestsByEmail(formData.ownerEmail);
        if (existingRequests.length > 0) {
          const pendingRequest = existingRequests.find(req => req.status === 'pending');
          if (pendingRequest) {
            newErrors.ownerEmail = 'A registration request with this email is already pending review';
          } else {
            const approvedRequest = existingRequests.find(req => req.status === 'approved');
            if (approvedRequest) {
              newErrors.ownerEmail = 'This email is already registered as a salon owner';
            }
          }
        }
      } catch (error) {
        console.error('Error checking duplicate email:', error);
        // Continue with validation, don't block submission for this check
      }
    }

    if (!formData.ownerPhone.trim()) {
      newErrors.ownerPhone = 'Phone number is required';
    } else if (!InputValidator.isValidPhone(formData.ownerPhone.trim())) {
      newErrors.ownerPhone = 'Please enter a valid phone number (minimum 10 digits)';
    }

    if (!formData.salonName.trim()) {
      newErrors.salonName = 'Salon name is required';
    } else if (formData.salonName.trim().length < 2) {
      newErrors.salonName = 'Salon name must be at least 2 characters long';
    }

    if (!formData.salonDescription.trim()) {
      newErrors.salonDescription = 'Salon description is required';
    } else if (formData.salonDescription.length < 50) {
      newErrors.salonDescription = 'Description must be at least 50 characters';
    }

    if (!formData.salonAddress.trim()) {
      newErrors.salonAddress = 'Salon address is required';
    } else if (formData.salonAddress.trim().length < 10) {
      newErrors.salonAddress = 'Please enter a complete address';
    }

    // Security validation: Check for potentially harmful content
    const fieldsToCheck = [
      { field: 'ownerName', value: formData.ownerName },
      { field: 'salonName', value: formData.salonName },
      { field: 'salonDescription', value: formData.salonDescription },
      { field: 'salonAddress', value: formData.salonAddress },
      { field: 'businessLicense', value: formData.businessLicense },
    ];

    fieldsToCheck.forEach(({ field, value }) => {
      if (value && !InputValidator.isSafeText(value)) {
        newErrors[field] = 'Invalid characters detected. Please remove any HTML or script content.';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Rate limiting using security utility
    const rateLimitKey = `salon_registration_${formData.ownerEmail}`;

    if (RateLimiter.isRateLimited(rateLimitKey, 3, 300000)) { // 3 attempts per 5 minutes
      const timeUntilReset = RateLimiter.getTimeUntilReset(rateLimitKey, 300000);
      const minutesRemaining = Math.ceil(timeUntilReset / 60000);
      toast.error(`Too many submission attempts. Please wait ${minutesRemaining} minutes before trying again.`);
      return;
    }

    setIsLoading(true);

    try {
      // Validate form with async checks
      const isValid = await validateForm();
      if (!isValid) {
        setIsLoading(false);
        return;
      }

      // Submit registration request to Firebase
      const requestId = await SalonRequestService.createSalonRequest({
        ownerName: formData.ownerName.trim(),
        ownerEmail: formData.ownerEmail.trim().toLowerCase(),
        ownerPhone: formData.ownerPhone.trim(),
        salonName: formData.salonName.trim(),
        salonDescription: formData.salonDescription.trim(),
        salonAddress: formData.salonAddress.trim(),
        businessLicense: formData.businessLicense.trim() || undefined,
      });

      console.log('Registration request submitted with ID:', requestId);

      setIsSubmitted(true);
      toast.success('Registration request submitted successfully! You will receive an email once your request is reviewed.');
    } catch (error: any) {
      console.error('Error submitting registration request:', error);

      // Handle specific error cases
      if (error.message?.includes('already exists') || error.message?.includes('duplicate')) {
        toast.error('A registration request with this email already exists.');
      } else if (error.message?.includes('network') || error.message?.includes('offline')) {
        toast.error('Network error. Please check your internet connection and try again.');
      } else {
        toast.error('Failed to submit registration request. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-glamspot-neutral-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Logo */}
          <div className="flex items-center justify-center gap-2 text-glamspot-primary mb-8">
            <div className="w-10 h-10 bg-glamspot-primary rounded-lg flex items-center justify-center">
              <Heart className="w-6 h-6 text-white fill-current" />
            </div>
            <h1 className="text-3xl font-bold tracking-tight">GlamSpot</h1>
          </div>

          <Card className="shadow-lg border-0">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl font-bold text-glamspot-neutral-900">
                Request Submitted!
              </CardTitle>
              <CardDescription className="text-glamspot-neutral-600">
                Your salon registration request has been submitted successfully
              </CardDescription>
            </CardHeader>
            
            <CardContent className="text-center space-y-4">
              <div className="bg-glamspot-neutral-50 p-4 rounded-lg">
                <p className="text-sm text-glamspot-neutral-700">
                  <strong>What happens next?</strong>
                </p>
                <ul className="text-sm text-glamspot-neutral-600 mt-2 space-y-1 text-left">
                  <li>• Our team will review your application within 2-3 business days</li>
                  <li>• We'll verify your business information and documentation</li>
                  <li>• You'll receive an email notification about the approval status</li>
                  <li>• Once approved, you can start managing your salon profile</li>
                </ul>
              </div>
              
              <div className="flex flex-col gap-3">
                <Button asChild>
                  <Link to="/">
                    Back to GlamSpot
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/login">
                    Sign In
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-glamspot-neutral-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Logo */}
        <div className="flex items-center justify-center gap-2 text-glamspot-primary mb-8">
          <div className="w-10 h-10 bg-glamspot-primary rounded-lg flex items-center justify-center">
            <Heart className="w-6 h-6 text-white fill-current" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight">GlamSpot</h1>
        </div>

        <Card className="shadow-lg border-0">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-glamspot-neutral-900">
              Join GlamSpot as a Salon Owner
            </CardTitle>
            <CardDescription className="text-center text-glamspot-neutral-600">
              Fill out this form to request your salon to be listed on our platform
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Owner Information */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-glamspot-neutral-900 font-semibold">
                  <User className="w-5 h-5 text-glamspot-primary" />
                  Owner Information
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ownerName" className="text-glamspot-neutral-700">
                      Full Name *
                    </Label>
                    <Input
                      id="ownerName"
                      type="text"
                      placeholder="Enter your full name"
                      value={formData.ownerName}
                      onChange={(e) => handleInputChange('ownerName', e.target.value)}
                      className={errors.ownerName ? 'border-red-500' : ''}
                    />
                    {errors.ownerName && (
                      <p className="text-sm text-red-600">{errors.ownerName}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="ownerPhone" className="text-glamspot-neutral-700">
                      Phone Number *
                    </Label>
                    <Input
                      id="ownerPhone"
                      type="tel"
                      placeholder="+****************"
                      value={formData.ownerPhone}
                      onChange={(e) => handleInputChange('ownerPhone', e.target.value)}
                      className={errors.ownerPhone ? 'border-red-500' : ''}
                    />
                    {errors.ownerPhone && (
                      <p className="text-sm text-red-600">{errors.ownerPhone}</p>
                    )}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="ownerEmail" className="text-glamspot-neutral-700">
                    Email Address *
                  </Label>
                  <Input
                    id="ownerEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.ownerEmail}
                    onChange={(e) => handleInputChange('ownerEmail', e.target.value)}
                    className={errors.ownerEmail ? 'border-red-500' : ''}
                  />
                  {errors.ownerEmail && (
                    <p className="text-sm text-red-600">{errors.ownerEmail}</p>
                  )}
                </div>
              </div>

              {/* Salon Information */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-glamspot-neutral-900 font-semibold">
                  <Building2 className="w-5 h-5 text-glamspot-primary" />
                  Salon Information
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="salonName" className="text-glamspot-neutral-700">
                    Salon Name *
                  </Label>
                  <Input
                    id="salonName"
                    type="text"
                    placeholder="Enter your salon name"
                    value={formData.salonName}
                    onChange={(e) => handleInputChange('salonName', e.target.value)}
                    className={errors.salonName ? 'border-red-500' : ''}
                  />
                  {errors.salonName && (
                    <p className="text-sm text-red-600">{errors.salonName}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="salonAddress" className="text-glamspot-neutral-700">
                    Salon Address *
                  </Label>
                  <Input
                    id="salonAddress"
                    type="text"
                    placeholder="123 Main Street, City, State, ZIP"
                    value={formData.salonAddress}
                    onChange={(e) => handleInputChange('salonAddress', e.target.value)}
                    className={errors.salonAddress ? 'border-red-500' : ''}
                  />
                  {errors.salonAddress && (
                    <p className="text-sm text-red-600">{errors.salonAddress}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="salonDescription" className="text-glamspot-neutral-700">
                    Salon Description * (minimum 50 characters)
                  </Label>
                  <Textarea
                    id="salonDescription"
                    placeholder="Describe your salon, services, and what makes it special..."
                    value={formData.salonDescription}
                    onChange={(e) => handleInputChange('salonDescription', e.target.value)}
                    className={`min-h-[100px] ${errors.salonDescription ? 'border-red-500' : ''}`}
                  />
                  <div className="flex justify-between items-center">
                    {errors.salonDescription && (
                      <p className="text-sm text-red-600">{errors.salonDescription}</p>
                    )}
                    <p className="text-sm text-glamspot-neutral-500 ml-auto">
                      {formData.salonDescription.length}/50 characters
                    </p>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="businessLicense" className="text-glamspot-neutral-700">
                    Business License Number (optional)
                  </Label>
                  <Input
                    id="businessLicense"
                    type="text"
                    placeholder="Enter your business license number"
                    value={formData.businessLicense}
                    onChange={(e) => handleInputChange('businessLicense', e.target.value)}
                  />
                </div>
              </div>

              <Alert>
                <FileText className="h-4 w-4" />
                <AlertDescription>
                  By submitting this form, you agree to our terms of service and confirm that 
                  all information provided is accurate. Our team will review your application 
                  and contact you within 2-3 business days.
                </AlertDescription>
              </Alert>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  type="submit"
                  className="flex-1 bg-glamspot-primary hover:bg-glamspot-primary-dark"
                  disabled={isLoading}
                >
                  {isLoading ? 'Submitting...' : 'Submit Registration Request'}
                </Button>
                <Button type="button" variant="outline" asChild>
                  <Link to="/">Cancel</Link>
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SalonOwnerRegistration;
