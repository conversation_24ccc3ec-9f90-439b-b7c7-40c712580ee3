import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { Review } from '@/types';

export class ReviewService {
  private static readonly COLLECTION = 'reviews';

  // Create a new review
  static async createReview(reviewData: Omit<Review, 'id' | 'createdAt' | 'updatedAt' | 'isApproved'>): Promise<string> {
    try {
      const review: Omit<Review, 'id'> = {
        ...reviewData,
        isApproved: true, // Reviews are automatically approved
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      return await FirestoreService.create<Review>(this.COLLECTION, review);
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  }

  // Get review by ID
  static async getReviewById(id: string): Promise<Review | null> {
    try {
      return await FirestoreService.getById<Review>(this.COLLECTION, id);
    } catch (error) {
      console.error('Error getting review:', error);
      throw error;
    }
  }

  // Get all reviews for a salon (no approval needed)
  static async getApprovedReviewsBySalon(salonId: string): Promise<Review[]> {
    try {
      return await FirestoreService.getWithQuery<Review>(this.COLLECTION, [
        where('salonId', '==', salonId),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting reviews for salon:', error);
      throw error;
    }
  }

  // Get all reviews for a salon (for admin/salon owner)
  static async getAllReviewsBySalon(salonId: string): Promise<Review[]> {
    try {
      return await FirestoreService.getWithQuery<Review>(this.COLLECTION, [
        where('salonId', '==', salonId),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting all reviews for salon:', error);
      throw error;
    }
  }

  // Get pending reviews (for admin approval)
  static async getPendingReviews(): Promise<Review[]> {
    try {
      return await FirestoreService.getWithQuery<Review>(this.COLLECTION, [
        where('isApproved', '==', false),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting pending reviews:', error);
      throw error;
    }
  }

  // Approve a review
  static async approveReview(id: string): Promise<void> {
    try {
      await FirestoreService.update<Review>(this.COLLECTION, id, {
        isApproved: true
      });
    } catch (error) {
      console.error('Error approving review:', error);
      throw error;
    }
  }

  // Delete a review
  static async deleteReview(id: string): Promise<void> {
    try {
      await FirestoreService.delete(this.COLLECTION, id);
    } catch (error) {
      console.error('Error deleting review:', error);
      throw error;
    }
  }

  // Update review
  static async updateReview(id: string, updates: Partial<Review>): Promise<void> {
    try {
      await FirestoreService.update<Review>(this.COLLECTION, id, updates);
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  }

  // Calculate average rating for a salon
  static async calculateSalonRating(salonId: string): Promise<{ averageRating: number; totalReviews: number }> {
    try {
      const reviews = await this.getApprovedReviewsBySalon(salonId);
      
      if (reviews.length === 0) {
        return { averageRating: 0, totalReviews: 0 };
      }

      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = Math.round((totalRating / reviews.length) * 10) / 10; // Round to 1 decimal place

      return { averageRating, totalReviews: reviews.length };
    } catch (error) {
      console.error('Error calculating salon rating:', error);
      throw error;
    }
  }

  // Listen to reviews changes for a salon
  static onSalonReviewsChange(salonId: string, callback: (reviews: Review[]) => void): () => void {
    return FirestoreService.onCollectionChange<Review>(
      this.COLLECTION,
      callback,
      [
        where('salonId', '==', salonId),
        orderBy('createdAt', 'desc')
      ]
    );
  }
}
