import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { SearchFilterProvider } from "@/contexts/SearchFilterContext";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import ErrorBoundary from "@/components/ErrorBoundary";

// Public pages
import Index from "./pages/Index";
import SalonDetails from "./pages/SalonDetails";
import BookingPage from "./pages/BookingPage";
import LoginPage from "./pages/LoginPage";
import UnauthorizedPage from "./pages/UnauthorizedPage";
import SalonOwnerRegistration from "./pages/SalonOwnerRegistration";
import NotFound from "./pages/NotFound";

// Salon owner pages
import SalonOwnerLayout from "./components/salon-owner/SalonOwnerLayout";
import SalonOwnerDashboard from "./pages/salon-owner/SalonOwnerDashboard";
import SalonOwnerProfile from "./pages/salon-owner/SalonOwnerProfile";
import SalonOwnerServices from "./pages/salon-owner/SalonOwnerServices";
import SalonOwnerStaff from "./pages/salon-owner/SalonOwnerStaff";
import SalonOwnerNotifications from "./pages/salon-owner/SalonOwnerNotifications";
import SalonOwnerBookings from "./pages/salon-owner/SalonOwnerBookings";
import SalonOwnerPricing from "./pages/salon-owner/SalonOwnerPricing";

// Admin pages
import AdminLayout from "./components/admin/AdminLayout";
import AdminDashboard from "./pages/admin/AdminDashboard";
import SalonManagement from "./pages/admin/SalonManagement";
import AdminSalonDetails from "./pages/admin/SalonDetails";
import ServiceManagement from "./pages/admin/ServiceManagement";
import StaffManagement from "./pages/admin/StaffManagement";
import BookingManagement from "./pages/admin/BookingManagement";
import Analytics from "./pages/admin/Analytics";
import SalonRequests from "./pages/admin/SalonRequests";
import SalonContentReview from "./pages/admin/SalonContentReview";

const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NotificationProvider>
          <SearchFilterProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<Index />} />
            <Route path="/salon/:id" element={<SalonDetails />} />
            <Route path="/salon/:id/booking" element={<BookingPage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/unauthorized" element={<UnauthorizedPage />} />
            <Route path="/register-salon" element={<SalonOwnerRegistration />} />

            {/* Admin routes */}
            <Route path="/admin" element={
              <ProtectedRoute requireAdmin>
                <AdminLayout />
              </ProtectedRoute>
            }>
              <Route index element={<AdminDashboard />} />
              <Route path="salons" element={<SalonManagement />} />
              <Route path="salons/:id" element={<AdminSalonDetails />} />
              <Route path="services" element={<ServiceManagement />} />
              <Route path="staff" element={<StaffManagement />} />
              <Route path="bookings" element={<BookingManagement />} />
              <Route path="analytics" element={<Analytics />} />
              <Route path="requests" element={<SalonRequests />} />
              <Route path="content-review" element={<SalonContentReview />} />
            </Route>

            {/* Salon owner routes */}
            <Route path="/salon-owner" element={
              <ProtectedRoute requireSalonOwner>
                <SalonOwnerLayout />
              </ProtectedRoute>
            }>
              <Route index element={<SalonOwnerDashboard />} />
              <Route path="profile" element={<SalonOwnerProfile />} />
              <Route path="services" element={<SalonOwnerServices />} />
              <Route path="staff" element={<SalonOwnerStaff />} />
              <Route path="bookings" element={<SalonOwnerBookings />} />
              <Route path="pricing" element={<SalonOwnerPricing />} />
              <Route path="notifications" element={<SalonOwnerNotifications />} />
            </Route>

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
            </TooltipProvider>
          </SearchFilterProvider>
        </NotificationProvider>
      </AuthProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
