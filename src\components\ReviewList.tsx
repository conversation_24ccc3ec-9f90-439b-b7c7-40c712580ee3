import React, { useState, useEffect } from 'react';
import { Star, User } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ReviewService } from '@/services/reviewService';
import { Review } from '@/types';
import { InlineLoader } from '@/components/ui/loading-spinner';
import { toast } from 'sonner';

interface ReviewListProps {
  salonId: string;
  showAll?: boolean;
  maxReviews?: number;
}

export const ReviewList: React.FC<ReviewListProps> = ({ 
  salonId, 
  showAll = false, 
  maxReviews = 6 
}) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAllReviews, setShowAllReviews] = useState(showAll);

  useEffect(() => {
    const loadReviews = async () => {
      try {
        setLoading(true);
        const reviewsData = await ReviewService.getApprovedReviewsBySalon(salonId);
        setReviews(reviewsData);
      } catch (error) {
        console.error('Error loading reviews:', error);
        toast.error('Failed to load reviews');
      } finally {
        setLoading(false);
      }
    };

    loadReviews();

    // Set up real-time listener for reviews
    const unsubscribe = ReviewService.onSalonReviewsChange(salonId, (reviewsData) => {
      setReviews(reviewsData);
    });

    return () => unsubscribe();
  }, [salonId]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating
                ? 'text-yellow-500 fill-yellow-500'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return <InlineLoader text="Loading reviews..." />;
  }

  if (reviews.length === 0) {
    return (
      <div className="text-center py-8">
        <Star className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">No reviews yet. Be the first to leave a review!</p>
      </div>
    );
  }

  const displayedReviews = showAllReviews ? reviews : reviews.slice(0, maxReviews);
  const hasMoreReviews = reviews.length > maxReviews && !showAllReviews;

  return (
    <div className="space-y-6">
      {/* Reviews Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {displayedReviews.map((review) => (
          <Card key={review.id} className="border border-gray-200">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Header with name, rating, and date */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-gray-500" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{review.customerName}</h4>
                      <p className="text-sm text-gray-500">{formatDate(review.createdAt)}</p>
                    </div>
                  </div>
                  {renderStars(review.rating)}
                </div>

                {/* Review Comment */}
                <p className="text-gray-700 leading-relaxed">{review.comment}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Show More Button */}
      {hasMoreReviews && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => setShowAllReviews(true)}
            className="px-6"
          >
            Show all {reviews.length} reviews
          </Button>
        </div>
      )}

      {/* Show Less Button */}
      {showAllReviews && reviews.length > maxReviews && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => setShowAllReviews(false)}
            className="px-6"
          >
            Show less
          </Button>
        </div>
      )}
    </div>
  );
};
