import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  MapPin,
  Search,
  Navigation,
  Check,
  X,
  Crosshair,
  Map
} from 'lucide-react';
import { toast } from 'sonner';
import { LeafletMap } from './LeafletMap';

interface LocationPickerProps {
  initialCoordinates?: { lat: number; lng: number };
  onLocationSelect: (coordinates: { lat: number; lng: number }, address: string) => void;
  onCancel?: () => void;
}

interface MapLocation {
  lat: number;
  lng: number;
  address: string;
  neighborhood: string;
}

// Dodoma locations for selection
const dodomaSampleLocations: MapLocation[] = [
  { lat: -6.1630, lng: 35.7516, address: "Dodoma City Center", neighborhood: "Downtown" },
  { lat: -6.1500, lng: 35.7400, address: "Kikuyu Road, Dodoma", neighborhood: "Kikuyu" },
  { lat: -6.1800, lng: 35.7600, address: "Makole Area, Dodoma", neighborhood: "Makole" },
  { lat: -6.1400, lng: 35.7700, address: "Zuzu Street, Dodoma", neighborhood: "Zuzu" },
  { lat: -6.1900, lng: 35.7300, address: "Msalato Ward, Dodoma", neighborhood: "Msalato" },
  { lat: -6.1700, lng: 35.7800, address: "Miyuji Area, Dodoma", neighborhood: "Miyuji" },
  { lat: -6.1300, lng: 35.7200, address: "Nzuguni Street, Dodoma", neighborhood: "Nzuguni" },
  { lat: -6.2000, lng: 35.7900, address: "Hombolo Road, Dodoma", neighborhood: "Hombolo" },
];

export const LocationPicker: React.FC<LocationPickerProps> = ({
  initialCoordinates,
  onLocationSelect,
  onCancel
}) => {
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [mapCenter, setMapCenter] = useState(
    initialCoordinates || { lat: -6.1630, lng: 35.7516 } // Dodoma default
  );

  // Initialize with existing coordinates if provided
  useEffect(() => {
    if (initialCoordinates) {
      const existingLocation = dodomaSampleLocations.find(
        loc => Math.abs(loc.lat - initialCoordinates.lat) < 0.001 &&
               Math.abs(loc.lng - initialCoordinates.lng) < 0.001
      );
      if (existingLocation) {
        setSelectedLocation(existingLocation);
      }
    }
  }, [initialCoordinates]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    
    // Simulate API search delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock search results
    const searchResults = dodomaSampleLocations.filter(location =>
      location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.neighborhood.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (searchResults.length > 0) {
      const firstResult = searchResults[0];
      setMapCenter({ lat: firstResult.lat, lng: firstResult.lng });
      setSelectedLocation(firstResult);
      toast.success(`Found location: ${firstResult.address}`);
    } else {
      toast.error('Location not found. Please try a different search term.');
    }
    
    setIsSearching(false);
  };

  const handleMapClick = (lat: number, lng: number) => {
    // Find the closest sample location to the clicked point
    const closest = dodomaSampleLocations.reduce((prev, curr) => {
      const prevDistance = Math.sqrt(
        Math.pow(prev.lat - lat, 2) + Math.pow(prev.lng - lng, 2)
      );
      const currDistance = Math.sqrt(
        Math.pow(curr.lat - lat, 2) + Math.pow(curr.lng - lng, 2)
      );
      return currDistance < prevDistance ? curr : prev;
    });

    setSelectedLocation(closest);
    setMapCenter({ lat: closest.lat, lng: closest.lng });
  };

  const handleConfirmLocation = () => {
    if (selectedLocation) {
      onLocationSelect(
        { lat: selectedLocation.lat, lng: selectedLocation.lng },
        selectedLocation.address
      );
      toast.success('Location saved successfully!');
    }
  };

  const handleUseCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          
          // For demo purposes, we'll use a mock location close to the user's position
          const mockCurrentLocation: MapLocation = {
            lat: latitude,
            lng: longitude,
            address: "Current Location (GPS)",
            neighborhood: "Current Area"
          };
          
          setSelectedLocation(mockCurrentLocation);
          setMapCenter({ lat: latitude, lng: longitude });
          toast.success('Current location detected!');
        },
        (error) => {
          toast.error('Unable to get current location. Please search manually.');
        }
      );
    } else {
      toast.error('Geolocation is not supported by this browser.');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5 text-glamspot-primary" />
            Set Salon Location
          </CardTitle>
          <CardDescription>
            Choose your salon's exact location for customers to find you easily
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Bar */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="location-search">Search for your address</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="location-search"
                  placeholder="Enter address, neighborhood, or landmark..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button 
                  onClick={handleSearch} 
                  disabled={isSearching}
                  className="bg-glamspot-primary hover:bg-glamspot-primary-dark"
                >
                  <Search className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="flex flex-col justify-end">
              <Button 
                variant="outline" 
                onClick={handleUseCurrentLocation}
                className="whitespace-nowrap"
              >
                <Navigation className="w-4 h-4 mr-2" />
                Use Current
              </Button>
            </div>
          </div>

          {/* Leaflet Map */}
          <div className="relative">
            <div className="w-full h-96 rounded-lg overflow-hidden border border-glamspot-neutral-200">
              <LeafletMap
                salons={dodomaSampleLocations.map(location => ({
                  id: location.neighborhood,
                  name: location.neighborhood,
                  location: location.address,
                  coordinates: { lat: location.lat, lng: location.lng },
                  rating: 5.0
                }))}
                selectedSalon={selectedLocation?.neighborhood || null}
                onSalonSelect={(salonId) => {
                  const location = dodomaSampleLocations.find(loc => loc.neighborhood === salonId);
                  if (location) {
                    handleMapClick(location.lat, location.lng);
                  }
                }}
                center={mapCenter}
                zoom={12}
                height="384px"
              />
            </div>

            {/* Map Instructions */}
            <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-md z-10">
              <div className="flex items-center gap-2 text-sm text-glamspot-neutral-700">
                <Map className="w-4 h-4" />
                <span>Click on a marker to select location</span>
              </div>
            </div>
          </div>

          {/* Selected Location Info */}
          {selectedLocation && (
            <Card className="border-glamspot-primary/20 bg-glamspot-primary/5">
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-glamspot-primary rounded-full flex items-center justify-center">
                      <MapPin className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-glamspot-neutral-900">{selectedLocation.neighborhood}</h4>
                      <p className="text-sm text-glamspot-neutral-600">{selectedLocation.address}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-glamspot-neutral-500">
                        <span>Lat: {selectedLocation.lat.toFixed(4)}</span>
                        <span>Lng: {selectedLocation.lng.toFixed(4)}</span>
                      </div>
                    </div>
                  </div>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Selected
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
            )}
            <Button 
              onClick={handleConfirmLocation}
              disabled={!selectedLocation}
              className="bg-glamspot-primary hover:bg-glamspot-primary-dark"
            >
              <Check className="w-4 h-4 mr-2" />
              Confirm Location
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
