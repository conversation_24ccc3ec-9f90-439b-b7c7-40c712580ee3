import { SalonRequestService } from './salonRequestService';
import { AuthService } from './authService';
import { SalonService } from './salonService';
import { NotificationService } from './notificationService';
import { AuditLogService } from './auditLogService';
import { SalonRegistrationRequest, SalonOwner, Salon } from '@/types';

export class AdminApprovalService {
  // Approve salon registration request and create salon owner account
  static async approveSalonRegistration(
    requestId: string,
    adminId: string,
    adminName: string
  ): Promise<{
    salonOwnerId: string;
    salonId: string;
    tempPassword: string;
  }> {
    try {
      // Input validation
      if (!requestId?.trim()) {
        throw new Error('Request ID is required');
      }
      if (!adminId?.trim()) {
        throw new Error('Admin ID is required');
      }
      if (!adminName?.trim()) {
        throw new Error('Admin name is required');
      }

      // Get the registration request
      const request = await SalonRequestService.getSalonRequestById(requestId);
      if (!request) {
        throw new Error('Registration request not found');
      }

      if (request.status !== 'pending') {
        throw new Error(`Request has already been processed with status: ${request.status}`);
      }

      // Additional validation
      if (!request.ownerEmail || !request.ownerName || !request.salonName) {
        throw new Error('Invalid request data: missing required fields');
      }

      // Generate temporary password
      const tempPassword = AuthService.generateTempPassword();

      // Create salon owner account
      const salonOwnerData: Partial<SalonOwner> = {
        name: request.ownerName,
        registrationRequestId: requestId,
        tempPassword: tempPassword,
        mustChangePassword: true,
      };

      const salonOwner = await AuthService.createSalonOwnerAccount(
        request.ownerEmail,
        tempPassword,
        salonOwnerData,
        adminId
      );

      if (!salonOwner) {
        throw new Error('Failed to create salon owner account');
      }

      // Create initial salon record
      const salonData = {
        name: request.salonName,
        description: request.salonDescription,
        location: request.salonAddress,
        address: request.salonAddress,
        distance: '0 km',
        images: [],
        ownerId: salonOwner.id,
        isApproved: false,
        approvalStatus: 'pending' as const,
        registrationRequestId: requestId,
      };

      const salonId = await SalonService.createSalon(salonData);

      // Update salon owner with salon ID
      await AuthService.updateUserProfile(salonOwner.id, {
        salonId: salonId
      });

      // Update salon registration request status
      await SalonRequestService.approveSalonRequest(requestId, adminId);

      // Send notification to salon owner
      await this.sendApprovalNotification(
        request.ownerEmail,
        request.ownerName,
        tempPassword,
        adminName
      );

      // Create admin notification
      await NotificationService.createNotification({
        salonId: salonId,
        type: 'system',
        title: 'Salon Registration Approved',
        message: `Salon "${request.salonName}" registration has been approved. Owner account created for ${request.ownerName}.`,
        priority: 'medium',
        relatedId: requestId
      });

      // Log the approval action
      await AuditLogService.logSalonRegistrationApproval(
        adminId,
        adminName,
        requestId,
        salonId,
        salonOwner.id
      );

      // Log user creation
      await AuditLogService.logUserCreation(
        adminId,
        adminName,
        salonOwner.id,
        'salon_owner'
      );

      // Log salon creation
      await AuditLogService.logSalonCreation(
        adminId,
        adminName,
        salonId,
        request.salonName,
        salonOwner.id
      );

      return {
        salonOwnerId: salonOwner.id,
        salonId: salonId,
        tempPassword: tempPassword
      };
    } catch (error) {
      console.error('Error approving salon registration:', error);
      throw error;
    }
  }

  // Reject salon registration request
  static async rejectSalonRegistration(
    requestId: string,
    adminId: string,
    adminName: string,
    rejectionReason: string
  ): Promise<void> {
    try {
      // Get the registration request
      const request = await SalonRequestService.getSalonRequestById(requestId);
      if (!request) {
        throw new Error('Registration request not found');
      }

      if (request.status !== 'pending') {
        throw new Error('Request has already been processed');
      }

      // Update salon registration request status
      await SalonRequestService.rejectSalonRequest(requestId, adminId, rejectionReason);

      // Send rejection notification to salon owner
      await this.sendRejectionNotification(
        request.ownerEmail,
        request.ownerName,
        rejectionReason,
        adminName
      );

      // Log the rejection action
      await AuditLogService.logSalonRegistrationRejection(
        adminId,
        adminName,
        requestId,
        rejectionReason
      );

    } catch (error) {
      console.error('Error rejecting salon registration:', error);
      throw error;
    }
  }

  // Send approval notification email
  private static async sendApprovalNotification(
    email: string,
    ownerName: string,
    tempPassword: string,
    adminName: string
  ): Promise<void> {
    try {
      // In a real application, you would integrate with an email service like SendGrid, AWS SES, etc.
      // For now, we'll log the email content and create a system notification
      
      const emailContent = {
        to: email,
        subject: 'GlamSpot Salon Registration Approved! 🎉',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #8B5CF6;">Congratulations! Your salon registration has been approved!</h2>
            
            <p>Dear ${ownerName},</p>
            
            <p>We're excited to inform you that your salon registration request has been approved by our admin team.</p>
            
            <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #374151; margin-top: 0;">Your Account Details:</h3>
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Temporary Password:</strong> <code style="background-color: #E5E7EB; padding: 4px 8px; border-radius: 4px;">${tempPassword}</code></p>
            </div>
            
            <p><strong>Next Steps:</strong></p>
            <ol>
              <li>Sign in to your salon owner dashboard using the credentials above</li>
              <li>Change your password immediately after first login</li>
              <li>Complete your salon profile with detailed information</li>
              <li>Add your services, staff, and pricing</li>
              <li>Submit your salon for final content review</li>
            </ol>
            
            <p>Once your salon content is reviewed and approved, it will be visible to customers on our platform.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${window.location.origin}/login" style="background-color: #8B5CF6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Sign In to Your Dashboard</a>
            </div>
            
            <p>If you have any questions, please don't hesitate to contact our support team.</p>
            
            <p>Welcome to GlamSpot!</p>
            <p>The GlamSpot Team</p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #E5E7EB;">
            <p style="font-size: 12px; color: #6B7280;">This email was sent by ${adminName} on behalf of the GlamSpot admin team.</p>
          </div>
        `
      };

      console.log('Approval email to be sent:', emailContent);
      
      // TODO: Integrate with actual email service
      // await EmailService.sendEmail(emailContent);
      
    } catch (error) {
      console.error('Error sending approval notification:', error);
      // Don't throw error here as the main approval process should still succeed
    }
  }

  // Send rejection notification email
  private static async sendRejectionNotification(
    email: string,
    ownerName: string,
    rejectionReason: string,
    adminName: string
  ): Promise<void> {
    try {
      const emailContent = {
        to: email,
        subject: 'GlamSpot Salon Registration Update',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #DC2626;">Salon Registration Update</h2>
            
            <p>Dear ${ownerName},</p>
            
            <p>Thank you for your interest in joining GlamSpot. After careful review, we are unable to approve your salon registration request at this time.</p>
            
            <div style="background-color: #FEF2F2; border-left: 4px solid #DC2626; padding: 20px; margin: 20px 0;">
              <h3 style="color: #DC2626; margin-top: 0;">Reason for rejection:</h3>
              <p style="margin-bottom: 0;">${rejectionReason}</p>
            </div>
            
            <p>If you believe this decision was made in error or if you have additional information that might change our assessment, please feel free to contact our support team.</p>
            
            <p>You may also submit a new registration request once you have addressed the concerns mentioned above.</p>
            
            <p>Thank you for your understanding.</p>
            <p>The GlamSpot Team</p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #E5E7EB;">
            <p style="font-size: 12px; color: #6B7280;">This email was sent by ${adminName} on behalf of the GlamSpot admin team.</p>
          </div>
        `
      };

      console.log('Rejection email to be sent:', emailContent);
      
      // TODO: Integrate with actual email service
      // await EmailService.sendEmail(emailContent);
      
    } catch (error) {
      console.error('Error sending rejection notification:', error);
      // Don't throw error here as the main rejection process should still succeed
    }
  }

  // Approve salon content for public visibility
  static async approveSalonContent(
    salonId: string,
    adminId: string
  ): Promise<void> {
    try {
      const salon = await SalonService.getSalonById(salonId);
      if (!salon) {
        throw new Error('Salon not found');
      }

      // Update salon approval status
      await SalonService.updateSalon(salonId, {
        isApproved: true,
        approvalStatus: 'approved',
        approvedAt: new Date().toISOString(),
        approvedBy: adminId,
        contentReviewStatus: 'approved',
        contentReviewedAt: new Date().toISOString(),
        contentReviewedBy: adminId
      });

      // Create notification for salon owner
      await NotificationService.createNotification({
        salonId: salonId,
        type: 'system',
        title: 'Salon Content Approved',
        message: 'Your salon content has been approved and is now visible to customers!',
        priority: 'high',
        relatedId: salonId
      });

      // Log the content approval action
      await AuditLogService.logSalonContentApproval(
        adminId,
        'Admin', // We don't have admin name here, could be improved
        salonId
      );

    } catch (error) {
      console.error('Error approving salon content:', error);
      throw error;
    }
  }

  // Reject salon content
  static async rejectSalonContent(
    salonId: string,
    adminId: string,
    rejectionReason: string
  ): Promise<void> {
    try {
      const salon = await SalonService.getSalonById(salonId);
      if (!salon) {
        throw new Error('Salon not found');
      }

      // Update salon approval status
      await SalonService.updateSalon(salonId, {
        isApproved: false,
        approvalStatus: 'content_review',
        contentReviewStatus: 'rejected',
        contentReviewedAt: new Date().toISOString(),
        contentReviewedBy: adminId,
        rejectionReason: rejectionReason
      });

      // Create notification for salon owner
      await NotificationService.createNotification({
        salonId: salonId,
        type: 'system',
        title: 'Salon Content Needs Updates',
        message: `Your salon content requires updates: ${rejectionReason}`,
        priority: 'high',
        relatedId: salonId
      });

      // Log the content rejection action
      await AuditLogService.logSalonContentRejection(
        adminId,
        'Admin', // We don't have admin name here, could be improved
        salonId,
        rejectionReason
      );

    } catch (error) {
      console.error('Error rejecting salon content:', error);
      throw error;
    }
  }
}
