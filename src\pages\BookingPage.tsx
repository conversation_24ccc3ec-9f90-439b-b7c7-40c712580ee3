import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { Star, ChevronLeft, ChevronRight, Calendar, ArrowRight, Search, Bell, Menu } from "lucide-react";
import { Header } from "@/components/Header";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { SalonService } from '@/services/salonService';
import { ServiceService } from '@/services/serviceService';
import { StaffService } from '@/services/staffService';
import { BookingService } from '@/services/bookingService';
import { NotificationService } from '@/services/notificationService';
import { Salon, Service, Staff } from '@/types';
import { InlineLoader } from '@/components/ui/loading-spinner';
import { toast } from 'sonner';

// Sample data - in a real app, this would come from an API
const salons = [
  {
    id: "1",
    name: "The Hair Lounge",
    location: "San Francisco",
    services: [
      { id: "haircut", name: "Haircut", price: 50 },
      { id: "manicure", name: "Manicure", price: 35 },
      { id: "facial", name: "Facial", price: 80 },
      { id: "massage", name: "Massage", price: 90 },
      { id: "waxing", name: "Waxing", price: 45 }
    ],
    stylists: [
      { id: "emily", name: "Emily Carter", specialty: "Specializes in haircuts" },
      { id: "sophia", name: "Sophia Bennett", specialty: "Expert in manicures" },
      { id: "olivia", name: "Olivia Hayes", specialty: "Facial and massage specialist" }
    ]
  }
];

const timeSlots = [
  "09:00 AM",
  "10:00 AM", 
  "11:00 AM",
  "02:00 PM",
  "03:00 PM"
];

const BookingPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [salon, setSalon] = useState<Salon | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [loading, setLoading] = useState(true);

  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectedStylist, setSelectedStylist] = useState("");
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [selectedTime, setSelectedTime] = useState("");
  const [customerName, setCustomerName] = useState("");
  const [customerPhone, setCustomerPhone] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load salon data
  useEffect(() => {
    const loadSalonData = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Load salon data, services, and staff
        const [salonData, servicesData, staffData] = await Promise.all([
          SalonService.getSalonById(id),
          ServiceService.getServicesBySalon(id),
          StaffService.getStaffBySalon(id)
        ]);

        if (salonData) {
          setSalon(salonData);
          setServices(servicesData);
          setStaff(staffData);

          // Set first staff member as default if available
          if (staffData.length > 0) {
            setSelectedStylist(staffData[0].id);
          }
        } else {
          toast.error('Salon not found');
          navigate('/');
        }
      } catch (error) {
        console.error('Error loading salon data:', error);
        toast.error('Failed to load salon data');
        navigate('/');
      } finally {
        setLoading(false);
      }
    };

    loadSalonData();
  }, [id, navigate]);

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const calculateTotal = () => {
    return selectedServices.reduce((total, serviceId) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service?.price || 0);
    }, 0);
  };

  const handleBookingSubmit = async () => {
    if (!salon || selectedServices.length === 0 || !selectedStylist || !selectedDate || !selectedTime || !customerName.trim() || !customerPhone.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create booking data
      const bookingData = {
        salonId: salon.id,
        customerName: customerName.trim(),
        customerEmail: '', // No email since users don't sign up
        customerPhone: customerPhone.trim(),
        serviceId: selectedServices[0], // For now, use first service. In future, support multiple services
        staffId: selectedStylist,
        date: format(selectedDate, 'yyyy-MM-dd'),
        time: selectedTime,
        status: 'pending' as const,
        totalAmount: calculateTotal(),
        notes: selectedServices.length > 1 ? `Multiple services: ${selectedServices.map(id => {
          const service = services.find(s => s.id === id);
          return service?.name;
        }).join(', ')}` : undefined
      };

      // Create the booking
      const bookingId = await BookingService.createBooking(bookingData);

      // Create notification for salon owner
      await NotificationService.createNotification({
        salonId: salon.id,
        type: 'booking',
        title: 'New Booking Request',
        message: `New booking from ${customerName} for ${format(selectedDate, 'MMMM d, yyyy')} at ${selectedTime}`,
        isNew: true,
        priority: 'medium',
        relatedId: bookingId
      });

      // Create notification for admin (assuming admin has a special salon ID or we create a general admin notification)
      await NotificationService.createNotification({
        salonId: 'admin', // Special ID for admin notifications
        type: 'booking',
        title: 'New Booking in System',
        message: `New booking at ${salon.name} from ${customerName}`,
        isNew: true,
        priority: 'low',
        relatedId: bookingId
      });

      toast.success('Booking submitted successfully! The salon will contact you to confirm your appointment.');

      // Reset form
      setSelectedServices([]);
      setSelectedStylist(staff.length > 0 ? staff[0].id : '');
      setSelectedDate(undefined);
      setSelectedTime('');
      setCustomerName('');
      setCustomerPhone('');

    } catch (error) {
      console.error('Error submitting booking:', error);
      toast.error('Failed to submit booking. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-glamspot-neutral-50">
        <Header />
        <div className="flex justify-center items-center py-20">
          <InlineLoader text="Loading booking page..." />
        </div>
      </div>
    );
  }

  if (!salon) {
    return (
      <div className="min-h-screen bg-glamspot-neutral-50">
        <Header />
        <div className="flex justify-center items-center py-20">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Salon not found</h2>
            <Button onClick={() => navigate('/')}>Go back to home</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-glamspot-neutral-50">
      <Header />
      
      <main className="flex-1">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <div className="lg:col-span-2 space-y-8">
              {/* Header */}
              <div className="border-b border-glamspot-neutral-200 pb-8">
                <h1 className="text-3xl font-bold tracking-tight text-glamspot-neutral-900 sm:text-4xl">
                  Book Your Appointment
                </h1>
                <p className="mt-2 text-lg text-glamspot-neutral-500">
                  Choose your services and preferred stylist to get started.
                </p>
              </div>

              {/* Services Selection */}
              <div className="space-y-6" id="services">
                <h2 className="text-2xl font-bold text-glamspot-neutral-900">1. Select Services</h2>
                <div className="flex flex-wrap gap-3">
                  {services.map((service) => (
                    <Button
                      key={service.id}
                      variant="outline"
                      onClick={() => handleServiceToggle(service.id)}
                      className={cn(
                        "rounded-full border border-glamspot-neutral-200 px-4 py-2 text-sm font-medium transition-colors",
                        selectedServices.includes(service.id)
                          ? "border-glamspot-primary bg-glamspot-primary text-white hover:bg-glamspot-primary-dark"
                          : "hover:border-glamspot-primary hover:bg-glamspot-primary hover:text-white"
                      )}
                    >
                      {service.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Stylist Selection */}
              <div className="space-y-6" id="stylist">
                <h2 className="text-2xl font-bold text-glamspot-neutral-900">2. Choose a Stylist</h2>
                <RadioGroup value={selectedStylist} onValueChange={setSelectedStylist}>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {staff.map((stylist) => (
                      <div key={stylist.id}>
                        <RadioGroupItem value={stylist.id} id={stylist.id} className="sr-only" />
                        <Label
                          htmlFor={stylist.id}
                          className={cn(
                            "flex items-center gap-4 rounded-lg border border-glamspot-neutral-200 p-4 cursor-pointer transition-colors",
                            selectedStylist === stylist.id
                              ? "border-glamspot-primary ring-2 ring-glamspot-primary"
                              : "hover:border-glamspot-neutral-300"
                          )}
                        >
                          <div className={cn(
                            "h-5 w-5 rounded-full border-2 flex items-center justify-center",
                            selectedStylist === stylist.id
                              ? "border-glamspot-primary bg-glamspot-primary"
                              : "border-glamspot-neutral-300"
                          )}>
                            {selectedStylist === stylist.id && (
                              <div className="h-2 w-2 rounded-full bg-white" />
                            )}
                          </div>
                          <div className="flex-grow">
                            <p className="font-semibold text-glamspot-neutral-900">{stylist.name}</p>
                            <p className="text-sm text-glamspot-neutral-500">{stylist.specialty}</p>
                            {stylist.bio && (
                              <p className="text-xs text-glamspot-neutral-400 mt-1">{stylist.bio}</p>
                            )}
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>

              {/* Date and Time Selection */}
              <div className="space-y-6" id="date-time">
                <h2 className="text-2xl font-bold text-glamspot-neutral-900">3. Select Date and Time</h2>

                {/* Date Selection */}
                <div className="rounded-lg border border-glamspot-neutral-200 p-4 bg-white">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !selectedDate && "text-muted-foreground"
                        )}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={selectedDate}
                        onSelect={setSelectedDate}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Time Selection */}
                <div className="mt-4">
                  <Select value={selectedTime} onValueChange={setSelectedTime}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a time slot" />
                    </SelectTrigger>
                    <SelectContent>
                      {timeSlots.map((time) => (
                        <SelectItem key={time} value={time}>
                          {time}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Customer Details */}
              <div className="space-y-6" id="customer-details">
                <h2 className="text-2xl font-bold text-glamspot-neutral-900">4. Your Contact Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="customerName">Full Name *</Label>
                    <Input
                      id="customerName"
                      type="text"
                      placeholder="Enter your full name"
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="customerPhone">Phone Number *</Label>
                    <Input
                      id="customerPhone"
                      type="tel"
                      placeholder="Enter your phone number"
                      value={customerPhone}
                      onChange={(e) => setCustomerPhone(e.target.value)}
                      required
                    />
                  </div>
                </div>
                <p className="text-sm text-glamspot-neutral-500">
                  We'll use this information to contact you about your appointment.
                </p>
              </div>

              {/* Book Button */}
              <div className="flex justify-end pt-6">
                <Button
                  className="flex items-center justify-center gap-2 bg-glamspot-primary hover:bg-glamspot-primary-dark text-white font-bold px-6 py-3"
                  disabled={selectedServices.length === 0 || !selectedDate || !selectedTime || !customerName.trim() || !customerPhone.trim() || isSubmitting}
                  onClick={handleBookingSubmit}
                >
                  <span>{isSubmitting ? 'Submitting...' : 'Book Appointment'}</span>
                  {!isSubmitting && <ArrowRight className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* Booking Summary Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 rounded-lg border border-glamspot-neutral-200 bg-white shadow-lg p-6">
                <h2 className="text-2xl font-bold mb-6 text-glamspot-neutral-900">Booking Summary</h2>

                {/* Salon Info */}
                <div className="mb-6 pb-4 border-b border-glamspot-neutral-200">
                  <h3 className="font-semibold text-glamspot-neutral-900">{salon.name}</h3>
                  <p className="text-sm text-glamspot-neutral-500">{salon.location}</p>
                  {salon.address && (
                    <p className="text-xs text-glamspot-neutral-400 mt-1">{salon.address}</p>
                  )}
                </div>

                {/* Selected Services */}
                {selectedServices.length > 0 ? (
                  <div className="mb-6">
                    <h4 className="font-medium text-glamspot-neutral-900 mb-3">Selected Services</h4>
                    <div className="space-y-2">
                      {selectedServices.map((serviceId) => {
                        const service = services.find(s => s.id === serviceId);
                        return service ? (
                          <div key={serviceId} className="flex justify-between text-sm">
                            <span className="text-glamspot-neutral-700">{service.name}</span>
                            <span className="font-medium text-glamspot-neutral-900">${service.price}</span>
                          </div>
                        ) : null;
                      })}
                    </div>
                  </div>
                ) : (
                  <div className="mb-6 text-center py-4">
                    <p className="text-sm text-glamspot-neutral-500">No services selected</p>
                  </div>
                )}

                {/* Selected Stylist */}
                {selectedStylist && (
                  <div className="mb-6">
                    <h4 className="font-medium text-glamspot-neutral-900 mb-2">Selected Stylist</h4>
                    {(() => {
                      const stylist = staff.find(s => s.id === selectedStylist);
                      return stylist ? (
                        <div className="text-sm">
                          <p className="text-glamspot-neutral-700">{stylist.name}</p>
                          <p className="text-xs text-glamspot-neutral-500">{stylist.specialty}</p>
                        </div>
                      ) : null;
                    })()}
                  </div>
                )}

                {/* Selected Date & Time */}
                {(selectedDate || selectedTime) && (
                  <div className="mb-6">
                    <h4 className="font-medium text-glamspot-neutral-900 mb-2">Appointment Details</h4>
                    <div className="text-sm space-y-1">
                      {selectedDate && (
                        <p className="text-glamspot-neutral-700">
                          Date: {format(selectedDate, "MMMM d, yyyy")}
                        </p>
                      )}
                      {selectedTime && (
                        <p className="text-glamspot-neutral-700">Time: {selectedTime}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Customer Details */}
                {(customerName || customerPhone) && (
                  <div className="mb-6">
                    <h4 className="font-medium text-glamspot-neutral-900 mb-2">Contact Information</h4>
                    <div className="text-sm space-y-1">
                      {customerName && (
                        <p className="text-glamspot-neutral-700">Name: {customerName}</p>
                      )}
                      {customerPhone && (
                        <p className="text-glamspot-neutral-700">Phone: {customerPhone}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Total */}
                {selectedServices.length > 0 && (
                  <div className="pt-4 border-t border-glamspot-neutral-200">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-glamspot-neutral-900">Total</span>
                      <span className="text-xl font-bold text-glamspot-primary">${calculateTotal()}</span>
                    </div>
                  </div>
                )}


              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default BookingPage;
