/**
 * Security utilities for input validation and sanitization
 */

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]{10,}$/,
  name: /^[a-zA-Z\s\-']{2,50}$/,
  businessName: /^[a-zA-Z0-9\s\-'&.]{2,100}$/,
  address: /^[a-zA-Z0-9\s\-',.#]{5,200}$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
};

// Input sanitization functions
export class InputSanitizer {
  /**
   * Remove potentially harmful characters from text input
   */
  static sanitizeText(input: string, maxLength: number = 1000): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .replace(/[<>\"'&]/g, '') // Remove HTML/script injection characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .substring(0, maxLength);
  }

  /**
   * Sanitize and validate email
   */
  static sanitizeEmail(email: string): string {
    if (!email || typeof email !== 'string') return '';
    
    return email
      .toLowerCase()
      .trim()
      .substring(0, 254); // RFC 5321 limit
  }

  /**
   * Sanitize phone number
   */
  static sanitizePhone(phone: string): string {
    if (!phone || typeof phone !== 'string') return '';
    
    return phone
      .replace(/[^\d\s\-\(\)\+]/g, '')
      .trim()
      .substring(0, 20);
  }

  /**
   * Sanitize URL
   */
  static sanitizeUrl(url: string): string {
    if (!url || typeof url !== 'string') return '';
    
    const sanitized = url.trim().substring(0, 2048);
    
    // Ensure URL has protocol
    if (sanitized && !sanitized.match(/^https?:\/\//)) {
      return `https://${sanitized}`;
    }
    
    return sanitized;
  }

  /**
   * Sanitize HTML content (basic)
   */
  static sanitizeHtml(html: string): string {
    if (!html || typeof html !== 'string') return '';
    
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim()
      .substring(0, 10000);
  }

  /**
   * Sanitize file name
   */
  static sanitizeFileName(fileName: string): string {
    if (!fileName || typeof fileName !== 'string') return '';
    
    return fileName
      .replace(/[^a-zA-Z0-9\-_.]/g, '')
      .substring(0, 255);
  }
}

// Input validation functions
export class InputValidator {
  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    return VALIDATION_PATTERNS.email.test(email);
  }

  /**
   * Validate phone number format
   */
  static isValidPhone(phone: string): boolean {
    return VALIDATION_PATTERNS.phone.test(phone);
  }

  /**
   * Validate name format
   */
  static isValidName(name: string): boolean {
    return VALIDATION_PATTERNS.name.test(name);
  }

  /**
   * Validate business name format
   */
  static isValidBusinessName(name: string): boolean {
    return VALIDATION_PATTERNS.businessName.test(name);
  }

  /**
   * Validate address format
   */
  static isValidAddress(address: string): boolean {
    return VALIDATION_PATTERNS.address.test(address);
  }

  /**
   * Validate URL format
   */
  static isValidUrl(url: string): boolean {
    return VALIDATION_PATTERNS.url.test(url);
  }

  /**
   * Validate UUID format
   */
  static isValidUuid(uuid: string): boolean {
    return VALIDATION_PATTERNS.uuid.test(uuid);
  }

  /**
   * Check if string contains only safe characters
   */
  static isSafeText(text: string): boolean {
    const dangerousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /data:text\/html/i,
      /vbscript:/i,
    ];
    
    return !dangerousPatterns.some(pattern => pattern.test(text));
  }

  /**
   * Validate password strength
   */
  static isStrongPassword(password: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Rate limiting utility
export class RateLimiter {
  private static attempts: Map<string, { count: number; lastAttempt: number }> = new Map();

  /**
   * Check if action is rate limited
   */
  static isRateLimited(
    key: string,
    maxAttempts: number = 5,
    windowMs: number = 60000 // 1 minute
  ): boolean {
    const now = Date.now();
    const attempt = this.attempts.get(key);

    if (!attempt) {
      this.attempts.set(key, { count: 1, lastAttempt: now });
      return false;
    }

    // Reset if window has passed
    if (now - attempt.lastAttempt > windowMs) {
      this.attempts.set(key, { count: 1, lastAttempt: now });
      return false;
    }

    // Increment attempt count
    attempt.count++;
    attempt.lastAttempt = now;

    return attempt.count > maxAttempts;
  }

  /**
   * Reset rate limit for a key
   */
  static resetRateLimit(key: string): void {
    this.attempts.delete(key);
  }

  /**
   * Get remaining attempts
   */
  static getRemainingAttempts(
    key: string,
    maxAttempts: number = 5
  ): number {
    const attempt = this.attempts.get(key);
    if (!attempt) return maxAttempts;
    
    return Math.max(0, maxAttempts - attempt.count);
  }

  /**
   * Get time until reset
   */
  static getTimeUntilReset(
    key: string,
    windowMs: number = 60000
  ): number {
    const attempt = this.attempts.get(key);
    if (!attempt) return 0;
    
    const timeElapsed = Date.now() - attempt.lastAttempt;
    return Math.max(0, windowMs - timeElapsed);
  }
}

// Security headers utility
export class SecurityHeaders {
  /**
   * Get security headers for API requests
   */
  static getSecurityHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    };
  }
}

// CSRF protection utility
export class CSRFProtection {
  private static token: string | null = null;

  /**
   * Generate CSRF token
   */
  static generateToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    this.token = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    return this.token;
  }

  /**
   * Get current CSRF token
   */
  static getToken(): string | null {
    return this.token;
  }

  /**
   * Validate CSRF token
   */
  static validateToken(token: string): boolean {
    return this.token !== null && this.token === token;
  }
}

// Export all utilities
export default {
  InputSanitizer,
  InputValidator,
  RateLimiter,
  SecurityHeaders,
  CSRFProtection,
  VALIDATION_PATTERNS,
};
