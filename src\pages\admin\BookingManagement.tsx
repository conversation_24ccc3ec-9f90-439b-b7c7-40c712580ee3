import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  MoreHorizontal, 
  Eye, 
  Check, 
  X, 
  Calendar,
  Clock,
  User,
  Building2,
  Phone,
  Mail
} from 'lucide-react';
import { Booking, Salon, Service, Staff } from '@/types';
import { BookingService } from '@/services/bookingService';
import { SalonService } from '@/services/salonService';
import { ServiceService } from '@/services/serviceService';
import { StaffService } from '@/services/staffService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';

const BookingManagement = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [salons, setSalons] = useState<Salon[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [loading, setLoading] = useState(true);

  // Load data from Firebase
  useEffect(() => {
    const loadData = async () => {
      if (!user || user.role !== 'admin') return;

      try {
        setLoading(true);
        const [bookingsData, salonsData, servicesData, staffData] = await Promise.all([
          BookingService.getAllBookings(),
          SalonService.getAllSalons(),
          ServiceService.getAllServices(),
          StaffService.getAllStaff()
        ]);

        setBookings(bookingsData);
        setSalons(salonsData);
        setServices(servicesData);
        setStaff(staffData);
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load booking data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user]);

  // Helper functions to get names from IDs
  const getSalonName = (salonId: string) => {
    const salon = salons.find(s => s.id === salonId);
    return salon?.name || 'Unknown Salon';
  };

  const getServiceName = (serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    return service?.name || 'Unknown Service';
  };

  const getStaffName = (staffId: string) => {
    const staffMember = staff.find(s => s.id === staffId);
    return staffMember?.name || 'Unknown Staff';
  };

  const filteredBookings = bookings.filter(booking => {
    const salonName = getSalonName(booking.salonId);
    const matchesSearch =
      booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      salonName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleViewBooking = (booking: Booking) => {
    setSelectedBooking(booking);
  };

  const handleConfirmBooking = (booking: Booking) => {
    // TODO: Implement confirm booking API call
    toast.success(`Booking for ${booking.customerName} has been confirmed`);
  };

  const handleCancelBooking = (booking: Booking) => {
    // TODO: Implement cancel booking API call
    toast.success(`Booking for ${booking.customerName} has been cancelled`);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-green-100 text-green-800',
      completed: 'bg-blue-100 text-blue-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || ''}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-glamspot-neutral-900">Booking Management</h1>
        <p className="text-glamspot-neutral-600 mt-2">
          View and manage all bookings across all salons
        </p>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-glamspot-neutral-400 w-4 h-4" />
              <Input
                placeholder="Search by customer, email, or salon..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Badge variant="outline" className="text-glamspot-neutral-600">
              {filteredBookings.length} booking{filteredBookings.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Bookings Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-glamspot-primary" />
            All Bookings
          </CardTitle>
          <CardDescription>
            Complete list of all bookings across all salons
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Salon</TableHead>
                <TableHead>Service</TableHead>
                <TableHead>Date & Time</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredBookings.map((booking) => (
                <TableRow key={booking.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-glamspot-neutral-200 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-glamspot-neutral-600" />
                      </div>
                      <div>
                        <p className="font-medium text-glamspot-neutral-900">{booking.customerName}</p>
                        <p className="text-sm text-glamspot-neutral-500">{booking.customerEmail}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building2 className="w-4 h-4 text-glamspot-neutral-500" />
                      <span className="text-sm">{getSalonName(booking.salonId)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium text-glamspot-neutral-900">{getServiceName(booking.serviceId)}</p>
                      <p className="text-sm text-glamspot-neutral-500">with {getStaffName(booking.staffId)}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-glamspot-neutral-500" />
                      <div>
                        <p className="text-sm font-medium">{new Date(booking.date).toLocaleDateString()}</p>
                        <p className="text-sm text-glamspot-neutral-500">{booking.time}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">${booking.totalAmount}</span>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(booking.status)}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewBooking(booking)}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        {booking.status === 'pending' && (
                          <DropdownMenuItem onClick={() => handleConfirmBooking(booking)}>
                            <Check className="w-4 h-4 mr-2" />
                            Confirm Booking
                          </DropdownMenuItem>
                        )}
                        {(booking.status === 'pending' || booking.status === 'confirmed') && (
                          <DropdownMenuItem
                            onClick={() => handleCancelBooking(booking)}
                            className="text-red-600"
                          >
                            <X className="w-4 h-4 mr-2" />
                            Cancel Booking
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Booking Details Dialog */}
      <Dialog open={!!selectedBooking} onOpenChange={() => setSelectedBooking(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Booking Details</DialogTitle>
            <DialogDescription>Complete information about this booking</DialogDescription>
          </DialogHeader>
          {selectedBooking && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Customer</label>
                    <div className="mt-1">
                      <p className="text-glamspot-neutral-900 font-medium">{selectedBooking.customerName}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Mail className="w-4 h-4 text-glamspot-neutral-500" />
                        <span className="text-sm text-glamspot-neutral-600">{selectedBooking.customerEmail}</span>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <Phone className="w-4 h-4 text-glamspot-neutral-500" />
                        <span className="text-sm text-glamspot-neutral-600">{selectedBooking.customerPhone}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Salon</label>
                    <p className="text-glamspot-neutral-900 mt-1">{getSalonName(selectedBooking.salonId)}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Service</label>
                    <p className="text-glamspot-neutral-900 mt-1">{getServiceName(selectedBooking.serviceId)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Staff Member</label>
                    <p className="text-glamspot-neutral-900 mt-1">{getStaffName(selectedBooking.staffId)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Date & Time</label>
                    <p className="text-glamspot-neutral-900 mt-1">
                      {new Date(selectedBooking.date).toLocaleDateString()} at {selectedBooking.time}
                    </p>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Total Amount</label>
                  <p className="text-glamspot-neutral-900 mt-1 text-lg font-semibold">${selectedBooking.totalAmount}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Status</label>
                  <div className="mt-1">
                    {getStatusBadge(selectedBooking.status)}
                  </div>
                </div>
              </div>
              {selectedBooking.notes && (
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Notes</label>
                  <p className="text-glamspot-neutral-900 mt-1">{selectedBooking.notes}</p>
                </div>
              )}
              <div className="flex justify-end gap-3 pt-4 border-t">
                {selectedBooking.status === 'pending' && (
                  <Button
                    onClick={() => handleConfirmBooking(selectedBooking)}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Confirm Booking
                  </Button>
                )}
                {(selectedBooking.status === 'pending' || selectedBooking.status === 'confirmed') && (
                  <Button
                    variant="destructive"
                    onClick={() => handleCancelBooking(selectedBooking)}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel Booking
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BookingManagement;
