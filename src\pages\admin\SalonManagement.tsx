import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  MapPin,
  Star,
  Building2
} from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Salon } from '@/types';
import { SalonService } from '@/services/salonService';
import { toast } from 'sonner';
import { InlineLoader } from '@/components/ui/loading-spinner';

const SalonManagement = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSalon, setSelectedSalon] = useState<Salon | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [salons, setSalons] = useState<Salon[]>([]);
  const [loading, setLoading] = useState(true);

  // Form state for adding salon
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    location: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    openingHours: '',
    amenities: [] as string[],
  });

  // Load salons from Firebase
  useEffect(() => {
    const loadSalons = async () => {
      try {
        setLoading(true);
        const salonsData = await SalonService.getAllSalons();
        setSalons(salonsData);
      } catch (error) {
        console.error('Error loading salons:', error);
        toast.error('Failed to load salons. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadSalons();

    // Set up real-time listener
    const unsubscribe = SalonService.onSalonsChange((salonsData) => {
      setSalons(salonsData);
    });

    return () => unsubscribe();
  }, []);

  const filteredSalons = salons.filter(salon =>
    salon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    salon.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleViewSalon = (salon: Salon) => {
    navigate(`/admin/salons/${salon.id}`);
  };

  const handleEditSalon = (salon: Salon) => {
    // TODO: Implement edit functionality
    console.log('Edit salon:', salon);
  };

  const handleDeleteSalon = async (salon: Salon) => {
    try {
      await SalonService.deleteSalon(salon.id);
      toast.success('Salon deleted successfully');
    } catch (error) {
      console.error('Error deleting salon:', error);
      toast.error('Failed to delete salon');
    }
  };

  const handleToggleStatus = async (salon: Salon) => {
    try {
      await SalonService.updateSalon(salon.id, { isActive: !salon.isActive });
      toast.success(`Salon ${salon.isActive ? 'deactivated' : 'activated'} successfully`);
    } catch (error) {
      console.error('Error updating salon status:', error);
      toast.error('Failed to update salon status');
    }
  };

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.location || !formData.address) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      await SalonService.createSalon({
        name: formData.name,
        description: formData.description,
        location: formData.location,
        address: formData.address,
        phone: formData.phone,
        email: formData.email,
        website: formData.website,
        openingHours: formData.openingHours,
        amenities: formData.amenities,
        images: [], // Default empty images array
        rating: 0, // Default rating
        isActive: true,
        coordinates: { lat: 0, lng: 0 }, // Default coordinates - would need geocoding
      });

      toast.success(`Salon "${formData.name}" has been added successfully`);
      setIsAddDialogOpen(false);

      // Reset form
      setFormData({
        name: '',
        description: '',
        location: '',
        address: '',
        phone: '',
        email: '',
        website: '',
        openingHours: '',
        amenities: [],
      });

    } catch (error) {
      console.error('Error creating salon:', error);
      toast.error('Failed to create salon. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <InlineLoader text="Loading salons..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Salon Management</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage all salon listings and their information
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
              <Plus className="w-4 h-4 mr-2" />
              Add Salon
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Salon</DialogTitle>
              <DialogDescription>
                Create a new salon listing in the system
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmitForm} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Salon Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter salon name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">Location *</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="e.g., Downtown, Mission District"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Address *</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="Full street address"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the salon and its services"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="(*************"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={formData.website}
                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                    placeholder="https://salon.com"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="openingHours">Opening Hours</Label>
                  <Input
                    id="openingHours"
                    value={formData.openingHours}
                    onChange={(e) => setFormData(prev => ({ ...prev, openingHours: e.target.value }))}
                    placeholder="Mon-Fri 9AM-6PM"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
                  Add Salon
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-glamspot-neutral-400 w-4 h-4" />
              <Input
                placeholder="Search salons by name or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="outline" className="text-glamspot-neutral-600">
              {filteredSalons.length} salon{filteredSalons.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Salons Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-glamspot-primary" />
            All Salons
          </CardTitle>
          <CardDescription>
            Complete list of all salon listings in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Salon</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSalons.map((salon) => (
                <TableRow key={salon.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-glamspot-neutral-200 rounded-lg flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-glamspot-neutral-600" />
                      </div>
                      <div>
                        <p className="font-medium text-glamspot-neutral-900">{salon.name}</p>
                        <p className="text-sm text-glamspot-neutral-500 truncate max-w-xs">
                          {salon.description}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-glamspot-neutral-600">
                      <MapPin className="w-4 h-4" />
                      <span className="text-sm">{salon.distance}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="font-medium">{salon.rating}</span>
                      <span className="text-sm text-glamspot-neutral-500">
                        ({salon.reviews})
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={salon.isActive ? 'default' : 'secondary'}
                      className={salon.isActive ? 'bg-green-100 text-green-800' : ''}
                    >
                      {salon.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-glamspot-neutral-500">
                    {new Date(salon.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewSalon(salon)}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditSalon(salon)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Salon
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleStatus(salon)}>
                          <Building2 className="w-4 h-4 mr-2" />
                          {salon.isActive ? 'Deactivate' : 'Activate'}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteSalon(salon)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Salon Details Dialog */}
      <Dialog open={!!selectedSalon} onOpenChange={() => setSelectedSalon(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedSalon?.name}</DialogTitle>
            <DialogDescription>Salon details and information</DialogDescription>
          </DialogHeader>
          {selectedSalon && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Location</label>
                  <p className="text-glamspot-neutral-900">{selectedSalon.location}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Rating</label>
                  <p className="text-glamspot-neutral-900">{selectedSalon.rating} ({selectedSalon.reviews} reviews)</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Address</label>
                <p className="text-glamspot-neutral-900">{selectedSalon.address}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Description</label>
                <p className="text-glamspot-neutral-900">{selectedSalon.description}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Status</label>
                <Badge 
                  variant={selectedSalon.isActive ? 'default' : 'secondary'}
                  className={selectedSalon.isActive ? 'bg-green-100 text-green-800 ml-2' : 'ml-2'}
                >
                  {selectedSalon.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SalonManagement;
