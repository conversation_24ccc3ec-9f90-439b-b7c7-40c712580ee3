import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { Salon, SalonForm } from '@/types';

export class SalonService {
  private static readonly COLLECTION = 'salons';

  // Create a new salon
  static async createSalon(salonData: SalonForm & { ownerId: string; registrationRequestId?: string }): Promise<string> {
    try {
      const salon: Omit<Salon, 'id'> = {
        ...salonData,
        rating: 0,
        reviews: 0,
        totalReviews: 0,
        isActive: true,
        isApproved: false,
        approvalStatus: 'pending',
        registrationRequestId: salonData.registrationRequestId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      return await FirestoreService.create<Salon>(this.COLLECTION, salon);
    } catch (error) {
      console.error('Error creating salon:', error);
      throw error;
    }
  }

  // Get salon by ID
  static async getSalonById(id: string): Promise<Salon | null> {
    try {
      return await FirestoreService.getById<Salon>(this.COLLECTION, id);
    } catch (error) {
      console.error('Error getting salon:', error);
      throw error;
    }
  }

  // Get all active salons (only approved salons for public display)
  static async getActiveSalons(): Promise<Salon[]> {
    try {
      // Get all salons and filter for active AND approved salons only
      const allSalons = await FirestoreService.getAll<Salon>(this.COLLECTION);
      return allSalons
        .filter(salon => salon.isActive && salon.isApproved)
        .sort((a, b) => (b.rating || 0) - (a.rating || 0));
    } catch (error) {
      console.error('Error getting active salons:', error);
      throw error;
    }
  }

  // Get salons by location
  static async getSalonsByLocation(location: string): Promise<Salon[]> {
    try {
      // Temporary workaround: Get all active salons and filter by location client-side
      const activeSalons = await this.getActiveSalons();
      return activeSalons.filter(salon => salon.location === location);
    } catch (error) {
      console.error('Error getting salons by location:', error);
      throw error;
    }
  }

  // Get salons by owner
  static async getSalonsByOwner(ownerId: string): Promise<Salon[]> {
    try {
      return await FirestoreService.getWithQuery<Salon>(this.COLLECTION, [
        where('ownerId', '==', ownerId),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting salons by owner:', error);
      throw error;
    }
  }

  // Update salon
  static async updateSalon(id: string, updates: Partial<Salon>): Promise<void> {
    try {
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await FirestoreService.update<Salon>(this.COLLECTION, id, updateData);
    } catch (error) {
      console.error('Error updating salon:', error);
      throw error;
    }
  }

  // Update salon content (triggers review workflow)
  static async updateSalonContent(
    id: string,
    updates: Partial<Salon>,
    ownerId: string
  ): Promise<void> {
    try {
      // Input validation
      if (!id?.trim()) {
        throw new Error('Salon ID is required');
      }
      if (!ownerId?.trim()) {
        throw new Error('Owner ID is required');
      }
      if (!updates || Object.keys(updates).length === 0) {
        throw new Error('No updates provided');
      }

      // Validate update fields
      if (updates.name && (!updates.name.trim() || updates.name.length < 2)) {
        throw new Error('Salon name must be at least 2 characters long');
      }
      if (updates.description && (!updates.description.trim() || updates.description.length < 10)) {
        throw new Error('Salon description must be at least 10 characters long');
      }
      if (updates.address && (!updates.address.trim() || updates.address.length < 5)) {
        throw new Error('Salon address must be at least 5 characters long');
      }

      // Get current salon to verify ownership
      const salon = await this.getSalonById(id);
      if (!salon) {
        throw new Error('Salon not found');
      }

      if (salon.ownerId !== ownerId) {
        throw new Error('Unauthorized: You can only update your own salon');
      }

      // Check if salon is active
      if (!salon.isActive) {
        throw new Error('Cannot update inactive salon');
      }

      const updateData = {
        ...updates,
        lastContentUpdate: new Date().toISOString(),
        contentReviewStatus: 'pending',
        updatedAt: new Date().toISOString()
      };

      await FirestoreService.update<Salon>(this.COLLECTION, id, updateData);
    } catch (error) {
      console.error('Error updating salon content:', error);
      throw error;
    }
  }

  // Delete salon (soft delete by setting isActive to false)
  static async deleteSalon(id: string): Promise<void> {
    try {
      await FirestoreService.update<Salon>(this.COLLECTION, id, { 
        isActive: false 
      });
    } catch (error) {
      console.error('Error deleting salon:', error);
      throw error;
    }
  }

  // Search salons by name
  static async searchSalons(searchTerm: string): Promise<Salon[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation - consider using Algolia or similar for better search
      const allSalons = await this.getActiveSalons();
      return allSalons.filter(salon => 
        salon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        salon.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        salon.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching salons:', error);
      throw error;
    }
  }

  // Get all salons (admin only)
  static async getAllSalons(): Promise<Salon[]> {
    try {
      return await FirestoreService.getAll<Salon>(this.COLLECTION);
    } catch (error) {
      console.error('Error getting all salons:', error);
      throw error;
    }
  }

  // Get approved salons only (for public display)
  static async getApprovedSalons(): Promise<Salon[]> {
    try {
      const allSalons = await FirestoreService.getAll<Salon>(this.COLLECTION);
      return allSalons
        .filter(salon => salon.isActive && salon.isApproved)
        .sort((a, b) => (b.rating || 0) - (a.rating || 0));
    } catch (error) {
      console.error('Error getting approved salons:', error);
      throw error;
    }
  }

  // Get salons pending content review (admin only)
  static async getSalonsPendingReview(): Promise<Salon[]> {
    try {
      const allSalons = await FirestoreService.getAll<Salon>(this.COLLECTION);
      return allSalons
        .filter(salon => salon.contentReviewStatus === 'pending')
        .sort((a, b) => new Date(b.lastContentUpdate || b.updatedAt).getTime() - new Date(a.lastContentUpdate || a.updatedAt).getTime());
    } catch (error) {
      console.error('Error getting salons pending review:', error);
      throw error;
    }
  }

  // Get salons by approval status (admin only)
  static async getSalonsByApprovalStatus(status: 'pending' | 'approved' | 'rejected' | 'content_review'): Promise<Salon[]> {
    try {
      const allSalons = await FirestoreService.getAll<Salon>(this.COLLECTION);
      return allSalons
        .filter(salon => salon.approvalStatus === status)
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
    } catch (error) {
      console.error('Error getting salons by approval status:', error);
      throw error;
    }
  }

  // Update salon rating
  static async updateSalonRating(salonId: string, newRating: number, reviewCount: number): Promise<void> {
    try {
      await FirestoreService.update<Salon>(this.COLLECTION, salonId, {
        rating: newRating,
        reviews: reviewCount
      });
    } catch (error) {
      console.error('Error updating salon rating:', error);
      throw error;
    }
  }

  // Listen to salon changes
  static onSalonChange(salonId: string, callback: (salon: Salon | null) => void): () => void {
    return FirestoreService.onDocumentChange<Salon>(this.COLLECTION, salonId, callback);
  }

  // Listen to salons collection changes (only approved salons for public display)
  static onSalonsChange(callback: (salons: Salon[]) => void): () => void {
    // Listen to all salons and filter for active AND approved salons only
    return FirestoreService.onCollectionChange<Salon>(this.COLLECTION, (allSalons) => {
      const activeSalons = allSalons
        .filter(salon => salon.isActive && salon.isApproved)
        .sort((a, b) => (b.rating || 0) - (a.rating || 0));
      callback(activeSalons);
    });
  }
}
