import { FirestoreService } from './firestoreService';
import { orderBy } from 'firebase/firestore';

export interface AuditLog {
  id: string;
  action: 'salon_registration_approved' | 'salon_registration_rejected' | 'salon_content_approved' | 'salon_content_rejected' | 'user_created' | 'salon_created';
  performedBy: string;
  performedByName: string;
  targetId: string;
  targetType: 'salon_registration_request' | 'salon' | 'user';
  details: Record<string, any>;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}

export class AuditLogService {
  private static readonly COLLECTION = 'audit_logs';

  // Create a new audit log entry
  static async createAuditLog(
    action: AuditLog['action'],
    performedBy: string,
    performedByName: string,
    targetId: string,
    targetType: AuditLog['targetType'],
    details: Record<string, any> = {},
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
    }
  ): Promise<string> {
    try {
      const auditLog: Omit<AuditLog, 'id'> = {
        action,
        performedBy,
        performedByName,
        targetId,
        targetType,
        details,
        timestamp: new Date().toISOString(),
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent
      };
      
      return await FirestoreService.create<AuditLog>(this.COLLECTION, auditLog);
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Don't throw error here as audit logging shouldn't break the main flow
      return '';
    }
  }

  // Get audit logs with pagination
  static async getAuditLogs(
    limit: number = 50,
    startAfter?: string
  ): Promise<AuditLog[]> {
    try {
      return await FirestoreService.getWithQuery<AuditLog>(
        this.COLLECTION,
        [orderBy('timestamp', 'desc')]
      );
    } catch (error) {
      console.error('Error getting audit logs:', error);
      throw error;
    }
  }

  // Get audit logs for a specific target
  static async getAuditLogsForTarget(
    targetId: string,
    targetType: AuditLog['targetType']
  ): Promise<AuditLog[]> {
    try {
      const allLogs = await this.getAuditLogs(1000);
      return allLogs.filter(log => log.targetId === targetId && log.targetType === targetType);
    } catch (error) {
      console.error('Error getting audit logs for target:', error);
      throw error;
    }
  }

  // Get audit logs by performer
  static async getAuditLogsByPerformer(performedBy: string): Promise<AuditLog[]> {
    try {
      const allLogs = await this.getAuditLogs(1000);
      return allLogs.filter(log => log.performedBy === performedBy);
    } catch (error) {
      console.error('Error getting audit logs by performer:', error);
      throw error;
    }
  }

  // Get audit logs by action type
  static async getAuditLogsByAction(action: AuditLog['action']): Promise<AuditLog[]> {
    try {
      const allLogs = await this.getAuditLogs(1000);
      return allLogs.filter(log => log.action === action);
    } catch (error) {
      console.error('Error getting audit logs by action:', error);
      throw error;
    }
  }

  // Get audit logs within date range
  static async getAuditLogsByDateRange(
    startDate: string,
    endDate: string
  ): Promise<AuditLog[]> {
    try {
      const allLogs = await this.getAuditLogs(1000);
      return allLogs.filter(log => 
        log.timestamp >= startDate && log.timestamp <= endDate
      );
    } catch (error) {
      console.error('Error getting audit logs by date range:', error);
      throw error;
    }
  }

  // Convenience methods for specific actions
  static async logSalonRegistrationApproval(
    adminId: string,
    adminName: string,
    requestId: string,
    salonId: string,
    salonOwnerId: string,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    await this.createAuditLog(
      'salon_registration_approved',
      adminId,
      adminName,
      requestId,
      'salon_registration_request',
      {
        salonId,
        salonOwnerId,
        action: 'Registration request approved and salon owner account created'
      },
      metadata
    );
  }

  static async logSalonRegistrationRejection(
    adminId: string,
    adminName: string,
    requestId: string,
    rejectionReason: string,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    await this.createAuditLog(
      'salon_registration_rejected',
      adminId,
      adminName,
      requestId,
      'salon_registration_request',
      {
        rejectionReason,
        action: 'Registration request rejected'
      },
      metadata
    );
  }

  static async logSalonContentApproval(
    adminId: string,
    adminName: string,
    salonId: string,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    await this.createAuditLog(
      'salon_content_approved',
      adminId,
      adminName,
      salonId,
      'salon',
      {
        action: 'Salon content approved and made public'
      },
      metadata
    );
  }

  static async logSalonContentRejection(
    adminId: string,
    adminName: string,
    salonId: string,
    rejectionReason: string,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    await this.createAuditLog(
      'salon_content_rejected',
      adminId,
      adminName,
      salonId,
      'salon',
      {
        rejectionReason,
        action: 'Salon content rejected with feedback'
      },
      metadata
    );
  }

  static async logUserCreation(
    adminId: string,
    adminName: string,
    userId: string,
    userRole: string,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    await this.createAuditLog(
      'user_created',
      adminId,
      adminName,
      userId,
      'user',
      {
        userRole,
        action: `${userRole} account created`
      },
      metadata
    );
  }

  static async logSalonCreation(
    adminId: string,
    adminName: string,
    salonId: string,
    salonName: string,
    ownerId: string,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    await this.createAuditLog(
      'salon_created',
      adminId,
      adminName,
      salonId,
      'salon',
      {
        salonName,
        ownerId,
        action: 'Salon record created during approval process'
      },
      metadata
    );
  }

  // Get audit statistics
  static async getAuditStats(): Promise<{
    totalLogs: number;
    logsByAction: Record<AuditLog['action'], number>;
    recentActivity: number; // Last 24 hours
    topPerformers: Array<{ performedBy: string; performedByName: string; count: number }>;
  }> {
    try {
      const allLogs = await this.getAuditLogs(1000);
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);
      const cutoffTime = twentyFourHoursAgo.toISOString();

      const logsByAction: Record<AuditLog['action'], number> = {
        salon_registration_approved: 0,
        salon_registration_rejected: 0,
        salon_content_approved: 0,
        salon_content_rejected: 0,
        user_created: 0,
        salon_created: 0
      };

      const performerCounts: Record<string, { name: string; count: number }> = {};

      allLogs.forEach(log => {
        logsByAction[log.action]++;
        
        if (!performerCounts[log.performedBy]) {
          performerCounts[log.performedBy] = { name: log.performedByName, count: 0 };
        }
        performerCounts[log.performedBy].count++;
      });

      const topPerformers = Object.entries(performerCounts)
        .map(([performedBy, data]) => ({
          performedBy,
          performedByName: data.name,
          count: data.count
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      return {
        totalLogs: allLogs.length,
        logsByAction,
        recentActivity: allLogs.filter(log => log.timestamp >= cutoffTime).length,
        topPerformers
      };
    } catch (error) {
      console.error('Error getting audit stats:', error);
      throw error;
    }
  }

  // Clean up old audit logs (older than specified days)
  static async cleanupOldLogs(daysToKeep: number = 365): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const cutoffDateString = cutoffDate.toISOString();

      const allLogs = await this.getAuditLogs(10000);
      const oldLogs = allLogs.filter(log => log.timestamp < cutoffDateString);

      if (oldLogs.length > 0) {
        const batchOperations = oldLogs.map(log => ({
          type: 'delete' as const,
          collection: this.COLLECTION,
          id: log.id
        }));

        await FirestoreService.batchWrite(batchOperations);
        console.log(`Cleaned up ${oldLogs.length} old audit logs`);
      }
    } catch (error) {
      console.error('Error cleaning up old audit logs:', error);
      throw error;
    }
  }
}
