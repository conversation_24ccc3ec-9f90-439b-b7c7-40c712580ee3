import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Save, 
  Edit, 
  Navigation, 
  Crosshair,
  AlertCircle,
  CheckCircle,
  Map
} from 'lucide-react';
import { LeafletMap } from '@/components/LeafletMap';
import { toast } from 'sonner';

interface LocationManagerProps {
  coordinates?: { lat: number; lng: number };
  address: string;
  salonName: string;
  onLocationChange: (coordinates: { lat: number; lng: number }, address?: string) => void;
  isEditing: boolean;
}

export const LocationManager: React.FC<LocationManagerProps> = ({
  coordinates,
  address,
  salonName,
  onLocationChange,
  isEditing
}) => {
  const [tempCoordinates, setTempCoordinates] = useState<{ lat: number; lng: number } | null>(
    coordinates || null
  );
  const [latInput, setLatInput] = useState(coordinates?.lat.toString() || '');
  const [lngInput, setLngInput] = useState(coordinates?.lng.toString() || '');
  const [isValidLocation, setIsValidLocation] = useState(false);
  const [isEditingCoords, setIsEditingCoords] = useState(false);

  useEffect(() => {
    if (coordinates) {
      setTempCoordinates(coordinates);
      setLatInput(coordinates.lat.toString());
      setLngInput(coordinates.lng.toString());
      setIsValidLocation(true);
    }
  }, [coordinates]);

  const validateCoordinates = (lat: number, lng: number): boolean => {
    return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
  };

  const handleCoordinateChange = () => {
    const lat = parseFloat(latInput);
    const lng = parseFloat(lngInput);

    if (isNaN(lat) || isNaN(lng)) {
      toast.error('Please enter valid numeric coordinates');
      setIsValidLocation(false);
      return;
    }

    if (!validateCoordinates(lat, lng)) {
      toast.error('Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180');
      setIsValidLocation(false);
      return;
    }

    const newCoordinates = { lat, lng };
    setTempCoordinates(newCoordinates);
    setIsValidLocation(true);
    toast.success('Coordinates updated! Preview the location on the map below.');
  };

  const handleSaveLocation = () => {
    if (!tempCoordinates || !isValidLocation) {
      toast.error('Please set valid coordinates first');
      return;
    }

    onLocationChange(tempCoordinates);
    setIsEditingCoords(false);
    toast.success('Location saved successfully!');
  };

  const handleUseCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newCoordinates = { lat: latitude, lng: longitude };
          
          setLatInput(latitude.toString());
          setLngInput(longitude.toString());
          setTempCoordinates(newCoordinates);
          setIsValidLocation(true);
          
          toast.success('Current location detected! You can now save this location.');
        },
        (error) => {
          toast.error('Unable to get current location. Please enter coordinates manually.');
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    } else {
      toast.error('Geolocation is not supported by this browser.');
    }
  };

  const getSampleCoordinates = () => {
    // Sample coordinates for major cities
    const samples = [
      { name: 'San Francisco, CA', lat: 37.7749, lng: -122.4194 },
      { name: 'Los Angeles, CA', lat: 34.0522, lng: -118.2437 },
      { name: 'New York, NY', lat: 40.7128, lng: -74.0060 },
      { name: 'Chicago, IL', lat: 41.8781, lng: -87.6298 },
      { name: 'Miami, FL', lat: 25.7617, lng: -80.1918 }
    ];
    return samples;
  };

  const handleUseSampleLocation = (sample: { name: string; lat: number; lng: number }) => {
    setLatInput(sample.lat.toString());
    setLngInput(sample.lng.toString());
    setTempCoordinates({ lat: sample.lat, lng: sample.lng });
    setIsValidLocation(true);
    toast.success(`Sample location set: ${sample.name}`);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="w-5 h-5 text-glamspot-primary" />
          Location Management
        </CardTitle>
        <CardDescription>
          Set your salon's exact location for customers to find you easily
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Location Display */}
        {coordinates && !isEditingCoords && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-900">Location Set</p>
                  <p className="text-sm text-green-700">
                    {address || 'Custom location'}
                  </p>
                  <p className="text-xs text-green-600">
                    Coordinates: {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
                  </p>
                </div>
              </div>
              {isEditing && (
                <Button
                  onClick={() => setIsEditingCoords(true)}
                  variant="outline"
                  size="sm"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Coordinate Input Section */}
        {isEditing && (isEditingCoords || !coordinates) && (
          <div className="space-y-4 p-4 border border-glamspot-neutral-200 rounded-lg bg-glamspot-neutral-50">
            <div className="flex items-center gap-2 mb-3">
              <Crosshair className="w-4 h-4 text-glamspot-primary" />
              <h3 className="font-medium text-glamspot-neutral-900">Set Coordinates</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="latitude">Latitude</Label>
                <Input
                  id="latitude"
                  placeholder="37.7749"
                  value={latInput}
                  onChange={(e) => setLatInput(e.target.value)}
                  onBlur={handleCoordinateChange}
                />
                <p className="text-xs text-glamspot-neutral-500 mt-1">
                  Range: -90 to 90
                </p>
              </div>
              <div>
                <Label htmlFor="longitude">Longitude</Label>
                <Input
                  id="longitude"
                  placeholder="-122.4194"
                  value={lngInput}
                  onChange={(e) => setLngInput(e.target.value)}
                  onBlur={handleCoordinateChange}
                />
                <p className="text-xs text-glamspot-neutral-500 mt-1">
                  Range: -180 to 180
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button
                onClick={handleCoordinateChange}
                variant="outline"
                size="sm"
              >
                <Map className="w-4 h-4 mr-1" />
                Update Preview
              </Button>
              <Button
                onClick={handleUseCurrentLocation}
                variant="outline"
                size="sm"
              >
                <Navigation className="w-4 h-4 mr-1" />
                Use Current Location
              </Button>
            </div>

            {/* Sample Locations */}
            <div>
              <p className="text-sm font-medium text-glamspot-neutral-700 mb-2">
                Or try a sample location:
              </p>
              <div className="flex flex-wrap gap-2">
                {getSampleCoordinates().map((sample) => (
                  <Button
                    key={sample.name}
                    onClick={() => handleUseSampleLocation(sample)}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                  >
                    {sample.name}
                  </Button>
                ))}
              </div>
            </div>

            {/* Save Button */}
            {tempCoordinates && isValidLocation && (
              <div className="flex gap-2 pt-2">
                <Button
                  onClick={handleSaveLocation}
                  className="bg-glamspot-primary hover:bg-glamspot-primary-dark"
                >
                  <Save className="w-4 h-4 mr-1" />
                  Save Location
                </Button>
                {coordinates && (
                  <Button
                    onClick={() => {
                      setIsEditingCoords(false);
                      setTempCoordinates(coordinates);
                      setLatInput(coordinates.lat.toString());
                      setLngInput(coordinates.lng.toString());
                    }}
                    variant="outline"
                  >
                    Cancel
                  </Button>
                )}
              </div>
            )}
          </div>
        )}

        {/* Map Preview */}
        <div>
          <h3 className="font-medium text-glamspot-neutral-900 mb-3">
            Location Preview
          </h3>
          
          {(tempCoordinates || coordinates) ? (
            <div className="space-y-3">
              <div className="h-64 rounded-lg overflow-hidden border border-glamspot-neutral-200">
                <LeafletMap
                  salons={[{
                    id: 'preview',
                    name: salonName,
                    location: address,
                    coordinates: tempCoordinates || coordinates!,
                    rating: 5.0
                  }]}
                  center={tempCoordinates || coordinates!}
                  zoom={15}
                  height="256px"
                />
              </div>
              
              {tempCoordinates && !coordinates && (
                <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <AlertCircle className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-blue-700">
                    This is a preview. Click "Save Location" to confirm this location.
                  </span>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 border border-glamspot-neutral-200 rounded-lg bg-glamspot-neutral-50">
              <MapPin className="h-12 w-12 text-glamspot-neutral-300 mx-auto mb-4" />
              <p className="text-glamspot-neutral-500">No location set</p>
              <p className="text-sm text-glamspot-neutral-400">
                {isEditing ? 'Enter coordinates above to preview your salon location' : 'Location will appear here once set'}
              </p>
            </div>
          )}
        </div>

        {/* Tips Section */}
        {isEditing && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">How to find your coordinates:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Use Google Maps: Right-click your location and copy coordinates</li>
              <li>• Use GPS apps on your phone at your salon location</li>
              <li>• Search online for "[Your Address] coordinates"</li>
              <li>• Use the "Current Location" button if you're at your salon</li>
              <li>• Accurate coordinates help customers find you easily</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
