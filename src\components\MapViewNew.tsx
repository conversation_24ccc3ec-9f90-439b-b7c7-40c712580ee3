import { useState } from "react";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Star, Heart, ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";
import { LeafletMap } from "./LeafletMap";

// Map coordinates for Dodoma areas
const locationCoordinates: Record<string, { lat: number; lng: number }> = {
  'Downtown': { lat: -6.1630, lng: 35.7516 },
  'Ki<PERSON>yu': { lat: -6.1500, lng: 35.7400 },
  'Makole': { lat: -6.1800, lng: 35.7600 },
  'Zuzu': { lat: -6.1400, lng: 35.7700 },
  'Msalato': { lat: -6.1900, lng: 35.7300 },
  '<PERSON><PERSON>ji': { lat: -6.1700, lng: 35.7800 },
  'Nzuguni': { lat: -6.1300, lng: 35.7200 },
  'Hombolo': { lat: -6.2000, lng: 35.7900 },
  'Bahi': { lat: -6.1100, lng: 35.8000 },
};

export const MapView = () => {
  const { filteredSalons } = useSearchFilter();
  const [selectedSalon, setSelectedSalon] = useState<string | null>(null);

  // Convert salon data to include coordinates
  const salonsWithCoords = filteredSalons.map(salon => ({
    ...salon,
    coordinates: salon.coordinates || locationCoordinates[salon.location] || locationCoordinates['Downtown'],
  }));

  const handleSalonClick = (salonId: string) => {
    setSelectedSalon(selectedSalon === salonId ? null : salonId);
  };

  return (
    <div className="relative">
      <LeafletMap
        salons={salonsWithCoords}
        selectedSalon={selectedSalon}
        onSalonSelect={handleSalonClick}
        center={{ lat: -6.1630, lng: 35.7516 }}
        zoom={13}
        height="600px"
      />

      {/* Selected Salon Details */}
      {selectedSalon && (
        <div className="absolute bottom-4 left-4 right-4 z-30">
          {(() => {
            const salon = salonsWithCoords.find(s => s.id === selectedSalon);
            if (!salon) return null;

            return (
              <Card className="bg-white shadow-lg">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg text-glamspot-neutral-900">{salon.name}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <MapPin className="w-4 h-4 text-glamspot-neutral-500" />
                        <span className="text-sm text-glamspot-neutral-600">{salon.location}</span>
                      </div>
                      <div className="flex items-center gap-4 mt-2">
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          <span className="text-sm font-medium">{salon.rating}</span>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          ${salon.price}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" asChild>
                        <Link to={`/salon/${salon.id}`}>
                          <ExternalLink className="w-4 h-4 mr-1" />
                          View
                        </Link>
                      </Button>
                      <Button size="sm" variant="ghost">
                        <Heart className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })()}
        </div>
      )}
    </div>
  );
};
