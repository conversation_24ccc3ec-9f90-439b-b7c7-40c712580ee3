rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Helper function to check if user is salon owner
    function isSalonOwner() {
      return request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'salon_owner';
    }

    // Helper function to check if salon owner is approved
    function isSalonOwnerApproved() {
      return isSalonOwner() &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isApproved == true;
    }

    // Helper function to get user's salon ID
    function getUserSalonId() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.salonId;
    }

    // Users collection - role-based access
    match /users/{userId} {
      // Users can read/write their own document
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // <PERSON><PERSON> can read all user documents
      allow read: if isAdmin();
      // Admins can update user approval status and salon assignment
      allow update: if isAdmin() &&
        (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isApproved', 'approvedAt', 'approvedBy', 'salonId']));
    }

    // Salon registration requests - public submission, admin management
    match /salon_registration_requests/{requestId} {
      // Anyone can submit a registration request
      allow create: if true;
      // Admins can read and update all requests
      allow read, update: if isAdmin();
      // Prevent deletion of requests for audit trail
      allow delete: if false;
    }

    // Salons collection - approved content only on public view
    match /salons/{salonId} {
      // Public read access only for approved and active salons
      allow read: if resource.data.isActive == true && resource.data.isApproved == true;
      // Salon owners can read their own salon (even if not approved)
      allow read: if isSalonOwner() && getUserSalonId() == salonId;
      // Admins can read all salons
      allow read: if isAdmin();
      // Only admins can create salons (during approval process)
      allow create: if isAdmin();
      // Salon owners can update their own salon data (triggers review workflow)
      allow update: if isSalonOwner() && getUserSalonId() == salonId && isSalonOwnerApproved();
      // Admins can update any salon (for approval workflow)
      allow update: if isAdmin();
      // No deletion allowed for audit trail
      allow delete: if false;
    }

    // Services collection - salon-specific access
    match /services/{serviceId} {
      // Public read access only for services of approved salons
      allow read: if get(/databases/$(database)/documents/salons/$(resource.data.salonId)).data.isApproved == true &&
                     get(/databases/$(database)/documents/salons/$(resource.data.salonId)).data.isActive == true;
      // Salon owners can manage their own salon's services
      allow read, create, update: if isSalonOwner() &&
        getUserSalonId() == resource.data.salonId && isSalonOwnerApproved();
      // Admins can manage all services
      allow read, create, update, delete: if isAdmin();
    }

    // Staff collection - salon-specific access
    match /staff/{staffId} {
      // Public read access only for staff of approved salons
      allow read: if get(/databases/$(database)/documents/salons/$(resource.data.salonId)).data.isApproved == true &&
                     get(/databases/$(database)/documents/salons/$(resource.data.salonId)).data.isActive == true;
      // Salon owners can manage their own salon's staff
      allow read, create, update: if isSalonOwner() &&
        getUserSalonId() == resource.data.salonId && isSalonOwnerApproved();
      // Admins can manage all staff
      allow read, create, update, delete: if isAdmin();
    }

    // Bookings collection - customer, salon owner, and admin access
    match /bookings/{bookingId} {
      // Customers can create bookings for approved salons only
      allow create: if request.auth != null &&
        get(/databases/$(database)/documents/salons/$(request.resource.data.salonId)).data.isApproved == true &&
        get(/databases/$(database)/documents/salons/$(request.resource.data.salonId)).data.isActive == true;
      // Customers can read their own bookings
      allow read: if request.auth != null &&
        (resource.data.customerId == request.auth.uid ||
         resource.data.customerEmail == get(/databases/$(database)/documents/users/$(request.auth.uid)).data.email);
      // Salon owners can read/update bookings for their salon
      allow read, update: if isSalonOwner() &&
        getUserSalonId() == resource.data.salonId && isSalonOwnerApproved();
      // Admins can manage all bookings
      allow read, update, delete: if isAdmin();
    }

    // Notifications collection - salon-specific access
    match /notifications/{notificationId} {
      // Salon owners can read their own salon's notifications
      allow read: if isSalonOwner() &&
        getUserSalonId() == resource.data.salonId && isSalonOwnerApproved();
      // Salon owners can update read status of their notifications
      allow update: if isSalonOwner() &&
        getUserSalonId() == resource.data.salonId && isSalonOwnerApproved() &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isRead', 'readAt']);
      // Admins can manage all notifications
      allow read, create, update, delete: if isAdmin();
    }

    // Pricing rules collection - salon-specific access
    match /pricing_rules/{ruleId} {
      // Salon owners can manage their own salon's pricing rules
      allow read, create, update: if isSalonOwner() &&
        getUserSalonId() == resource.data.salonId && isSalonOwnerApproved();
      // Admins can manage all pricing rules
      allow read, create, update, delete: if isAdmin();
    }

    // Analytics and reports - admin only
    match /analytics/{document=**} {
      allow read, write: if isAdmin();
    }

    // Audit logs - admin read-only
    match /audit_logs/{logId} {
      allow read: if isAdmin();
      allow create: if request.auth != null;
      allow update, delete: if false;
    }
  }
}